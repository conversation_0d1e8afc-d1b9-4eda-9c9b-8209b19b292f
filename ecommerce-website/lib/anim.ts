"use client"

import { animate, createTimeline, stagger } from "animejs"
import type { Timeline } from "animejs"

const baseEasing = "easeInOutQuad"

export function animateHoverScaleShadow(element: HTMLElement) {
  const enter = () => {
    animate(element, { scale: 1, duration: 0 }).cancel()
    animate(element, {
      scale: 1.03,
      boxShadow: [
        "0 0 0 rgba(0,0,0,0)",
        "0 8px 24px rgba(0,0,0,0.12)",
      ],
      duration: 250,
      ease: baseEasing,
    })
  }
  const leave = () => {
    animate(element, { scale: 1, duration: 0 }).cancel()
    animate(element, {
      scale: 1,
      boxShadow: "0 0 0 rgba(0,0,0,0)",
      duration: 250,
      ease: baseEasing,
    })
  }
  element.addEventListener("mouseenter", enter)
  element.addEventListener("mouseleave", leave)
  return () => {
    element.removeEventListener("mouseenter", enter)
    element.removeEventListener("mouseleave", leave)
  }
}

export function animateStaggerList(items: HTMLElement[] | NodeListOf<HTMLElement>) {
  animate(items as any, { translateY: 0, opacity: 1, duration: 0 }).cancel()
  animate(items as any, {
    translateY: [16, 0],
    opacity: [0, 1],
    duration: 300,
    ease: baseEasing,
    delay: stagger(60),
  })
}

export function animatePageEnter(container: HTMLElement) {
  animate(container, { translateY: 0, opacity: 1, duration: 0 }).cancel()
  return animate(container, {
    translateY: [12, 0],
    opacity: [0, 1],
    duration: 250,
    ease: baseEasing,
  })
}

export function animatePageLeave(container: HTMLElement) {
  animate(container, { translateY: 0, opacity: 1, duration: 0 }).cancel()
  return animate(container, {
    translateY: [0, 12],
    opacity: [1, 0],
    duration: 220,
    ease: baseEasing,
  })
}

export function animatePulseAttention(element: HTMLElement) {
  animate(element, { scale: 1, duration: 0 }).cancel()
  return animate(element, {
    scale: [1, 1.04, 1],
    duration: 400,
    ease: baseEasing,
  })
}

export function animateAddToCartClick(element: HTMLElement) {
  const tl: Timeline = createTimeline({ ease: "easeOutQuad" })
  tl.add(element, {
    scale: [1, 1.15, 1],
    duration: 180,
  }).add(element, {
    translateX: [0, -5, 5, -4, 4, -2, 2, 0],
    duration: 220,
  })
  return tl
}

export { animate }

// New helpers for product page

export function animateStaggerCards(items: HTMLElement[] | NodeListOf<HTMLElement>, opts?: { delay?: number; duration?: number }) {
  const prefersReduced = typeof window !== "undefined" && window.matchMedia("(prefers-reduced-motion: reduce)").matches
  if (prefersReduced) {
    ;(items as any).forEach((el: HTMLElement) => {
      el.style.opacity = "1"
      el.style.transform = "none"
    })
    return
  }
  animate(items as any, { translateY: 0, opacity: 1, duration: 0 }).cancel()
  animate(items as any, {
    translateY: [20, 0],
    opacity: [0, 1],
    duration: opts?.duration ?? 360,
    ease: "easeOutQuad",
    delay: stagger(opts?.delay ?? 180),
  })
}

export function attachCardHoverEffects(card: HTMLElement) {
  const prefersReduced = typeof window !== "undefined" && window.matchMedia("(prefers-reduced-motion: reduce)").matches
  if (prefersReduced) return () => {}
  const enter = () => {
    animate(card, { scale: 1, duration: 0 }).cancel()
    const tl = createTimeline({ ease: "easeOutQuad", duration: 220 })
    tl.add(card, { scale: 1.05, duration: 220 })
      .add(card, {
        boxShadow: [
          "0 0 0 rgba(0,0,0,0)",
          "0 10px 28px rgba(0,0,0,0.14)",
          "0 8px 24px rgba(0,0,0,0.10)",
        ],
        duration: 400,
      }, 0)
  }
  const leave = () => {
    animate(card, { scale: 1, duration: 0 }).cancel()
    animate(card, { scale: 1, boxShadow: "0 0 0 rgba(0,0,0,0)", duration: 220, ease: "easeOutQuad" })
  }
  card.addEventListener("mouseenter", enter)
  card.addEventListener("mouseleave", leave)
  return () => {
    card.removeEventListener("mouseenter", enter)
    card.removeEventListener("mouseleave", leave)
  }
}

export function attachButtonHoverPulse(button: HTMLElement) {
  const prefersReduced = typeof window !== "undefined" && window.matchMedia("(prefers-reduced-motion: reduce)").matches
  if (prefersReduced) return () => {}
  let pulse: Timeline | null = null
  const enter = () => {
    animate(button, { scale: 1, duration: 0 }).cancel()
    pulse = createTimeline({ ease: "easeInOutSine", loop: true })
      .add(button, { scale: 1.0, duration: 0 })
      .add(button, { scale: 1.05, duration: 600 })
      .add(button, { scale: 1.0, duration: 600 })
  }
  const leave = () => {
    if (pulse) pulse.pause()
    animate(button, { scale: 1, duration: 0 }).cancel()
    animate(button, { scale: 1, duration: 180, ease: "easeOutQuad" })
  }
  button.addEventListener("mouseenter", enter)
  button.addEventListener("mouseleave", leave)
  return () => {
    button.removeEventListener("mouseenter", enter)
    button.removeEventListener("mouseleave", leave)
  }
}

export function attachImageHoverEffects(img: HTMLElement) {
  const prefersReduced = typeof window !== "undefined" && window.matchMedia("(prefers-reduced-motion: reduce)").matches
  if (prefersReduced) return () => {}
  const enter = () => {
    animate(img, { scale: 1, duration: 0 }).cancel()
    const tl = createTimeline({ ease: "easeOutQuad" })
    tl.add(img, { scale: 1.05, duration: 240 })
      .add(img, {
        boxShadow: [
          "0 0 0 rgba(0,0,0,0)",
          "0 8px 24px rgba(0,0,0,0.12)",
          "0 6px 16px rgba(0,0,0,0.10)",
        ],
        duration: 380,
      }, 0)
  }
  const leave = () => {
    animate(img, { scale: 1, duration: 0 }).cancel()
    animate(img, { scale: 1, boxShadow: "0 0 0 rgba(0,0,0,0)", duration: 220, ease: "easeOutQuad" })
  }
  img.addEventListener("mouseenter", enter)
  img.addEventListener("mouseleave", leave)
  return () => {
    img.removeEventListener("mouseenter", enter)
    img.removeEventListener("mouseleave", leave)
  }
}


