"use client"

import anime from "animejs"
import type { AnimeTimelineInstance } from "animejs"

const baseEasing = "easeInOutQuad"

export function animateHoverScaleShadow(element: HTMLElement) {
  const enter = () => {
    anime.remove(element)
    anime({
      targets: element,
      scale: 1.03,
      boxShadow: [
        "0 0 0 rgba(0,0,0,0)",
        "0 8px 24px rgba(0,0,0,0.12)",
      ],
      duration: 250,
      easing: baseEasing,
    })
  }
  const leave = () => {
    anime.remove(element)
    anime({
      targets: element,
      scale: 1,
      boxShadow: "0 0 0 rgba(0,0,0,0)",
      duration: 250,
      easing: baseEasing,
    })
  }
  element.addEventListener("mouseenter", enter)
  element.addEventListener("mouseleave", leave)
  return () => {
    element.removeEventListener("mouseenter", enter)
    element.removeEventListener("mouseleave", leave)
  }
}

export function animateStaggerList(items: HTMLElement[] | NodeListOf<HTMLElement>) {
  anime.remove(items as any)
  anime({
    targets: items as any,
    translateY: [16, 0],
    opacity: [0, 1],
    duration: 300,
    easing: baseEasing,
    delay: anime.stagger(60),
  })
}

export function animatePageEnter(container: HTMLElement) {
  anime.remove(container)
  return anime({
    targets: container,
    translateY: [12, 0],
    opacity: [0, 1],
    duration: 250,
    easing: baseEasing,
  })
}

export function animatePageLeave(container: HTMLElement) {
  anime.remove(container)
  return anime({
    targets: container,
    translateY: [0, 12],
    opacity: [1, 0],
    duration: 220,
    easing: baseEasing,
  })
}

export function animatePulseAttention(element: HTMLElement) {
  anime.remove(element)
  return anime({
    targets: element,
    scale: [1, 1.04, 1],
    duration: 400,
    easing: baseEasing,
  })
}

export function animateAddToCartClick(element: HTMLElement) {
  const tl: AnimeTimelineInstance = anime.timeline({ easing: "easeOutQuad" })
  tl.add({
    targets: element,
    scale: [1, 1.15, 1],
    duration: 180,
  }).add({
    targets: element,
    translateX: [0, -5, 5, -4, 4, -2, 2, 0],
    duration: 220,
  })
  return tl
}

export { anime }

// New helpers for product page

export function animateStaggerCards(items: HTMLElement[] | NodeListOf<HTMLElement>, opts?: { delay?: number; duration?: number }) {
  const prefersReduced = typeof window !== "undefined" && window.matchMedia("(prefers-reduced-motion: reduce)").matches
  if (prefersReduced) {
    ;(items as any).forEach((el: HTMLElement) => {
      el.style.opacity = "1"
      el.style.transform = "none"
    })
    return
  }
  anime.remove(items as any)
  anime({
    targets: items as any,
    translateY: [20, 0],
    opacity: [0, 1],
    duration: opts?.duration ?? 360,
    easing: "easeOutQuad",
    delay: anime.stagger(opts?.delay ?? 180),
  })
}

export function attachCardHoverEffects(card: HTMLElement) {
  const prefersReduced = typeof window !== "undefined" && window.matchMedia("(prefers-reduced-motion: reduce)").matches
  if (prefersReduced) return () => {}
  const enter = () => {
    anime.remove(card)
    const tl = anime.timeline({ easing: "easeOutQuad", duration: 220 })
    tl.add({ targets: card, scale: 1.05, duration: 220 })
      .add({
        targets: card,
        boxShadow: [
          "0 0 0 rgba(0,0,0,0)",
          "0 10px 28px rgba(0,0,0,0.14)",
          "0 8px 24px rgba(0,0,0,0.10)",
        ],
        duration: 400,
      }, 0)
  }
  const leave = () => {
    anime.remove(card)
    anime({ targets: card, scale: 1, boxShadow: "0 0 0 rgba(0,0,0,0)", duration: 220, easing: "easeOutQuad" })
  }
  card.addEventListener("mouseenter", enter)
  card.addEventListener("mouseleave", leave)
  return () => {
    card.removeEventListener("mouseenter", enter)
    card.removeEventListener("mouseleave", leave)
  }
}

export function attachButtonHoverPulse(button: HTMLElement) {
  const prefersReduced = typeof window !== "undefined" && window.matchMedia("(prefers-reduced-motion: reduce)").matches
  if (prefersReduced) return () => {}
  let pulse: AnimeTimelineInstance | null = null
  const enter = () => {
    anime.remove(button)
    pulse = anime.timeline({ easing: "easeInOutSine", loop: true })
      .add({ targets: button, scale: 1.0, duration: 0 })
      .add({ targets: button, scale: 1.05, duration: 600 })
      .add({ targets: button, scale: 1.0, duration: 600 })
  }
  const leave = () => {
    if (pulse) pulse.pause()
    anime.remove(button)
    anime({ targets: button, scale: 1, duration: 180, easing: "easeOutQuad" })
  }
  button.addEventListener("mouseenter", enter)
  button.addEventListener("mouseleave", leave)
  return () => {
    button.removeEventListener("mouseenter", enter)
    button.removeEventListener("mouseleave", leave)
  }
}

export function attachImageHoverEffects(img: HTMLElement) {
  const prefersReduced = typeof window !== "undefined" && window.matchMedia("(prefers-reduced-motion: reduce)").matches
  if (prefersReduced) return () => {}
  const enter = () => {
    anime.remove(img)
    const tl = anime.timeline({ easing: "easeOutQuad" })
    tl.add({ targets: img, scale: 1.05, duration: 240 })
      .add({
        targets: img,
        boxShadow: [
          "0 0 0 rgba(0,0,0,0)",
          "0 8px 24px rgba(0,0,0,0.12)",
          "0 6px 16px rgba(0,0,0,0.10)",
        ],
        duration: 380,
      }, 0)
  }
  const leave = () => {
    anime.remove(img)
    anime({ targets: img, scale: 1, boxShadow: "0 0 0 rgba(0,0,0,0)", duration: 220, easing: "easeOutQuad" })
  }
  img.addEventListener("mouseenter", enter)
  img.addEventListener("mouseleave", leave)
  return () => {
    img.removeEventListener("mouseenter", enter)
    img.removeEventListener("mouseleave", leave)
  }
}


