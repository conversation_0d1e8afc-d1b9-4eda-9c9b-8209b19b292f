"use client"

import Link from "next/link"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Star, ShoppingCart, Trash2, Heart } from "lucide-react"
import { useWishlist } from "@/components/wishlist-context"
import { useCart } from "@/components/cart-context"
import { WishlistButton } from "@/components/wishlist-button"
import { toast } from "sonner"

export default function WishlistPage() {
  const { items, clear } = useWishlist()
  const { addItem } = useCart()

  const handleAddToCart = (product: any) => {
    addItem(product, 1)
    toast.success("Added to cart", { description: product.name })
  }

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    }).format(date)
  }

  return (
    <main className="py-12">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-slate-900 mb-2">My Wishlist</h1>
            <p className="text-slate-600">
              {items.length} item{items.length !== 1 ? 's' : ''} saved for later
            </p>
          </div>
          
          {items.length > 0 && (
            <Button 
              variant="outline" 
              onClick={clear}
              className="text-slate-600 hover:text-slate-900"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Clear All
            </Button>
          )}
        </div>

        {items.length === 0 ? (
          <div className="text-center py-16">
            <div className="w-24 h-24 bg-slate-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <Heart className="h-12 w-12 text-slate-400" />
            </div>
            <h2 className="text-xl font-semibold text-slate-900 mb-2">Your wishlist is empty</h2>
            <p className="text-slate-600 mb-6">
              Save items you love to your wishlist and shop them later.
            </p>
            <Link href="/products">
              <Button className="bg-slate-900 hover:bg-slate-800">
                Browse Products
              </Button>
            </Link>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {items.map((item) => (
              <Card key={item.productId} className="group transition-shadow duration-300 hover:shadow-lg">
                <CardContent className="p-0">
                  <div className="relative overflow-hidden rounded-t-lg">
                    <Link href={`/products/${item.product.slug}`}>
                      <img
                        src={item.product.images[0] || "/placeholder.svg"}
                        alt={item.product.name}
                        className="w-full h-48 object-cover transition-transform duration-300 group-hover:scale-105"
                      />
                    </Link>
                    
                    {/* Discount Badge */}
                    {item.product.compareAtPrice && item.product.compareAtPrice > item.product.price && (
                      <Badge className="absolute top-2 left-2 bg-red-500 hover:bg-red-600">
                        -{Math.round(((item.product.compareAtPrice - item.product.price) / item.product.compareAtPrice) * 100)}%
                      </Badge>
                    )}
                    
                    {/* Wishlist Button */}
                    <div className="absolute top-2 right-2">
                      <WishlistButton product={item.product} />
                    </div>
                    
                    {/* Stock Status */}
                    {item.product.inventory === 0 && (
                      <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                        <Badge variant="secondary" className="bg-white text-slate-900">
                          Out of Stock
                        </Badge>
                      </div>
                    )}
                  </div>

                  <div className="p-4">
                    <Link href={`/products/${item.product.slug}`} className="block">
                      <h3 className="font-semibold text-slate-900 mb-2 line-clamp-2 hover:text-slate-700 transition-colors">
                        {item.product.name}
                      </h3>
                    </Link>

                    {/* Rating */}
                    <div className="flex items-center mb-2">
                      <div className="flex items-center">
                        {[...Array(5)].map((_, i) => (
                          <Star
                            key={i}
                            className={`h-4 w-4 ${i < 4 ? "text-yellow-400 fill-current" : "text-slate-300"}`}
                          />
                        ))}
                      </div>
                      <span className="text-sm text-slate-600 ml-2">
                        ({Math.floor(Math.random() * 50) + 10})
                      </span>
                    </div>

                    {/* Price */}
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-2">
                        <span className="text-lg font-bold text-slate-900">
                          ${item.product.price.toFixed(2)}
                        </span>
                        {item.product.compareAtPrice && item.product.compareAtPrice > item.product.price && (
                          <span className="text-sm text-slate-500 line-through">
                            ${item.product.compareAtPrice.toFixed(2)}
                          </span>
                        )}
                      </div>
                    </div>

                    {/* Added Date */}
                    <div className="text-xs text-slate-500 mb-3">
                      Added {formatDate(item.addedAt)}
                    </div>

                    {/* Actions */}
                    <div className="flex gap-2">
                      <Button 
                        className="flex-1 bg-slate-900 hover:bg-slate-800"
                        onClick={() => handleAddToCart(item.product)}
                        disabled={item.product.inventory === 0}
                      >
                        <ShoppingCart className="h-4 w-4 mr-2" />
                        {item.product.inventory === 0 ? "Out of Stock" : "Add to Cart"}
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {/* Quick Actions */}
        {items.length > 0 && (
          <div className="mt-12 text-center">
            <div className="bg-slate-50 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-slate-900 mb-2">
                Ready to purchase?
              </h3>
              <p className="text-slate-600 mb-4">
                Add all your wishlist items to cart and checkout quickly.
              </p>
              <Button 
                className="bg-slate-900 hover:bg-slate-800"
                onClick={() => {
                  items.forEach(item => {
                    if (item.product.inventory > 0) {
                      addItem(item.product, 1)
                    }
                  })
                  toast.success("Added available items to cart")
                }}
              >
                Add All to Cart
              </Button>
            </div>
          </div>
        )}
      </div>
    </main>
  )
}
