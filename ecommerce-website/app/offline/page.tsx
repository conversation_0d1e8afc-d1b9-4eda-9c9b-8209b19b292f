import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { WifiOff, RefreshCw, Home, ShoppingBag } from "lucide-react"
import Link from "next/link"

export default function OfflinePage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 flex items-center justify-center p-4">
      <Card className="w-full max-w-md text-center">
        <CardHeader className="pb-4">
          <div className="w-16 h-16 bg-slate-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <WifiOff className="h-8 w-8 text-slate-600" />
          </div>
          <CardTitle className="text-xl text-slate-900">You're Offline</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <p className="text-slate-600">
            It looks like you've lost your internet connection. Don't worry, you can still browse 
            your cart and wishlist while offline.
          </p>

          <div className="space-y-3">
            <Button
              onClick={() => window.location.reload()}
              className="w-full"
              variant="default"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Try Again
            </Button>

            <div className="grid grid-cols-2 gap-3">
              <Button asChild variant="outline">
                <Link href="/">
                  <Home className="h-4 w-4 mr-2" />
                  Home
                </Link>
              </Button>
              
              <Button asChild variant="outline">
                <Link href="/cart">
                  <ShoppingBag className="h-4 w-4 mr-2" />
                  Cart
                </Link>
              </Button>
            </div>
          </div>

          <div className="pt-4 border-t border-slate-200">
            <h3 className="font-medium text-slate-900 mb-2">Available Offline:</h3>
            <ul className="text-sm text-slate-600 space-y-1">
              <li>• View your shopping cart</li>
              <li>• Browse your wishlist</li>
              <li>• Previously viewed products</li>
              <li>• Account information</li>
            </ul>
          </div>

          <div className="text-xs text-slate-500">
            Your changes will sync automatically when you're back online.
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export const metadata = {
  title: "Offline - ModernStore",
  description: "You're currently offline. Some features may be limited.",
}
