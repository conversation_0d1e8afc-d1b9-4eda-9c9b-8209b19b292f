"use client"

import Link from "next/link"
import { useState, useEffect, useRef } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet"
import { Star, ShoppingCart, Eye, Filter, Loader2 } from "lucide-react"
import { useSearch } from "@/components/search-context"
import { useCart } from "@/components/cart-context"
import { ProductFilters } from "@/components/product-filters"
import { WishlistButton } from "@/components/wishlist-button"
import { toast } from "sonner"
import {
  animateStaggerCards,
  attachCardHoverEffects,
  attachButtonHoverPulse,
  animateAddToCartClick,
  attachImageHoverEffects,
} from "@/lib/anim"

export default function ProductsPage() {
  const { filteredProducts, isLoading } = useSearch()
  const { addItem } = useCart()
  const [showMobileFilters, setShowMobileFilters] = useState(false)
  const gridRef = useRef<HTMLDivElement | null>(null)

  const handleAddToCart = (product: any, e: React.MouseEvent) => {
    animateAddToCartClick(e.currentTarget)
    addItem(product, 1)
    toast.success("Added to cart", { description: product.name })
  }

  return (
    <main className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="flex items-end justify-between gap-4 mb-8">
          <div>
            <h1 className="text-3xl lg:text-4xl font-bold text-slate-900 mb-2">All Products</h1>
            <p className="text-lg text-slate-600">Browse our catalog of premium products</p>
          </div>

          {/* Mobile Filter Button */}
          <Sheet open={showMobileFilters} onOpenChange={setShowMobileFilters}>
            <SheetTrigger asChild>
              <Button variant="outline" className="lg:hidden">
                <Filter className="h-4 w-4 mr-2" />
                Filters
              </Button>
            </SheetTrigger>
            <SheetContent side="left" className="w-80 p-0">
              <ProductFilters
                isMobile
                onClose={() => setShowMobileFilters(false)}
              />
            </SheetContent>
          </Sheet>
        </div>

        <div className="grid lg:grid-cols-4 gap-8">
          {/* Desktop Filters Sidebar */}
          <div className="hidden lg:block">
            <ProductFilters />
          </div>

          {/* Products Grid */}
          <div className="lg:col-span-3">
            {isLoading ? (
              <div className="flex items-center justify-center py-12">
                <Loader2 className="h-8 w-8 animate-spin text-slate-400" />
                <span className="ml-2 text-slate-600">Loading products...</span>
              </div>
            ) : filteredProducts.length === 0 ? (
              <div className="text-center py-12">
                <p className="text-slate-600 mb-4">No products found matching your criteria.</p>
                <Button variant="outline" onClick={() => window.location.reload()}>
                  Reset Filters
                </Button>
              </div>
            ) : (
              <div ref={gridRef} className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
                {filteredProducts.map((product) => (
            <Card key={product.id} className="group transition-shadow duration-300" data-anim-card>
              <CardContent className="p-0">
                <div className="relative overflow-hidden rounded-t-lg">
                  <Link href={`/products/${product.slug}`}>
                    <img
                      src={product.images[0] || "/placeholder.svg"}
                      alt={product.name}
                      className="w-full h-48 object-cover"
                      data-anim-img
                    />
                  </Link>
                  {product.compareAtPrice && product.compareAtPrice > product.price && (
                    <Badge className="absolute top-2 left-2 bg-red-500 hover:bg-red-600">
                      -{Math.round(((product.compareAtPrice - product.price) / product.compareAtPrice) * 100)}%
                    </Badge>
                  )}
                  <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity flex gap-1">
                    <WishlistButton product={product} />
                    <Link href={`/products/${product.slug}`}>
                      <Button size="sm" variant="secondary" className="h-8 w-8 p-0">
                        <Eye className="h-4 w-4" />
                      </Button>
                    </Link>
                  </div>
                </div>

                <div className="p-4">
                  <Link href={`/products/${product.slug}`} className="block">
                    <h3 className="font-semibold text-slate-900 mb-2 line-clamp-2">{product.name}</h3>
                  </Link>

                  <div className="flex items-center mb-2">
                    <div className="flex items-center">
                      {[...Array(5)].map((_, i) => (
                        <Star
                          key={i}
                          className={`h-4 w-4 ${i < 4 ? "text-yellow-400 fill-current" : "text-slate-300"}`}
                        />
                      ))}
                    </div>
                    <span className="text-sm text-slate-600 ml-2">({Math.floor(Math.random() * 50) + 10})</span>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <span className="text-lg font-bold text-slate-900">${product.price.toFixed(2)}</span>
                      {product.compareAtPrice && product.compareAtPrice > product.price && (
                        <span className="text-sm text-slate-500 line-through">${product.compareAtPrice.toFixed(2)}</span>
                      )}
                    </div>
                    <Button
                      size="sm"
                      className="bg-slate-900 hover:bg-slate-800"
                      data-anim-add
                      onClick={(e) => handleAddToCart(product, e)}
                    >
                      <ShoppingCart className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
                ))}
              </div>
            )}
          </div>
        </div>
        <ProductsPageAnimations gridRef={gridRef} />
      </div>
    </main>
  )
}

// Hook up animations to the real grid element
function ProductsPageAnimations({ gridRef }: { gridRef: React.RefObject<HTMLDivElement> }) {
  useEffect(() => {
    const grid = gridRef.current
    if (!grid) return
    const cards = grid.querySelectorAll<HTMLElement>("[data-anim-card]")
    const imgs = grid.querySelectorAll<HTMLElement>("[data-anim-img]")
    const buttons = grid.querySelectorAll<HTMLElement>("[data-anim-add]")

    const prefersReduced = typeof window !== "undefined" && window.matchMedia("(prefers-reduced-motion: reduce)").matches
    const runEntrance = () => {
      if (cards.length) {
        if (prefersReduced) {
          cards.forEach((el) => {
            el.style.opacity = "1"
            el.style.transform = "none"
          })
        } else {
          animateStaggerCards(cards, { delay: 180, duration: 360 })
        }
      }
    }
    const io = new IntersectionObserver((entries) => {
      const entry = entries[0]
      if (entry.isIntersecting) {
        io.disconnect()
        runEntrance()
      }
    }, { threshold: 0.2 })
    io.observe(grid)

    const cleanups: Array<() => void> = []
    cards.forEach((el) => cleanups.push(attachCardHoverEffects(el)))
    imgs.forEach((el) => cleanups.push(attachImageHoverEffects(el)))
    buttons.forEach((el) => {
      cleanups.push(attachButtonHoverPulse(el))
      el.addEventListener("click", () => animateAddToCartClick(el))
    })

    return () => {
      cleanups.forEach((fn) => fn())
      io.disconnect()
    }
  }, [gridRef])

  return null
}



