import Link from "next/link"
import { notFound } from "next/navigation"
import { Card, CardContent } from "@/components/ui/card"
import { mockCategories, mockProducts } from "@/lib/mock-data"

interface CategoryPageProps {
  params: { slug: string }
}

export default function CategoryDetailPage({ params }: CategoryPageProps) {
  const category = mockCategories.find((c) => c.slug === params.slug)
  if (!category) return notFound()
  const products = mockProducts.filter((p) => p.categoryId === category.id)

  return (
    <main className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="mb-12">
          <h1 className="text-3xl lg:text-4xl font-bold text-slate-900 mb-2">{category.name}</h1>
          {category.description ? <p className="text-lg text-slate-600">{category.description}</p> : null}
        </div>

        {products.length === 0 ? (
          <p className="text-slate-600">No products found in this category.</p>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {products.map((product) => (
              <Link key={product.id} href={`/products/${product.slug}`}>
                <Card className="group hover:shadow-lg transition-shadow duration-300">
                  <CardContent className="p-0">
                    <div className="relative overflow-hidden rounded-t-lg">
                      <img
                        src={product.images[0] || "/placeholder.svg"}
                        alt={product.name}
                        className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                      />
                    </div>
                    <div className="p-4">
                      <h3 className="font-semibold text-slate-900 mb-2 line-clamp-2">{product.name}</h3>
                      <div className="text-lg font-bold text-slate-900">${product.price.toFixed(2)}</div>
                    </div>
                  </CardContent>
                </Card>
              </Link>
            ))}
          </div>
        )}
      </div>
    </main>
  )
}




