import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ArrowR<PERSON>, Users, Award, Globe, Heart } from "lucide-react"

export default function AboutPage() {
  return (
    <main className="py-16">
      <div className="container mx-auto px-4">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <h1 className="text-4xl lg:text-6xl font-bold text-slate-900 mb-6">
            About <span className="bg-gradient-to-r from-slate-900 to-slate-600 bg-clip-text text-transparent">ModernStore</span>
          </h1>
          <p className="text-xl text-slate-600 max-w-3xl mx-auto">
            We're revolutionizing online shopping with immersive 3D experiences, premium products, and exceptional customer service.
          </p>
        </div>

        {/* Mission Section */}
        <div className="grid lg:grid-cols-2 gap-12 items-center mb-20">
          <div>
            <h2 className="text-3xl font-bold text-slate-900 mb-6">Our Mission</h2>
            <p className="text-lg text-slate-600 mb-6">
              To provide customers with an unparalleled shopping experience that combines cutting-edge technology, 
              premium quality products, and personalized service. We believe shopping should be engaging, 
              intuitive, and rewarding.
            </p>
            <p className="text-lg text-slate-600">
              Our platform leverages advanced 3D visualization technology to help customers make informed 
              decisions, reducing returns and increasing satisfaction.
            </p>
          </div>
          <div className="bg-gradient-to-br from-slate-100 to-slate-200 rounded-2xl p-8">
            <div className="aspect-square bg-white rounded-xl flex items-center justify-center">
              <div className="text-center">
                <div className="w-16 h-16 bg-slate-900 rounded-full mx-auto mb-4 flex items-center justify-center">
                  <Heart className="h-8 w-8 text-white" />
                </div>
                <h3 className="text-xl font-semibold text-slate-900">Customer First</h3>
                <p className="text-slate-600">Every decision we make puts our customers at the center</p>
              </div>
            </div>
          </div>
        </div>

        {/* Stats Section */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-6 mb-20">
          <Card className="text-center">
            <CardContent className="p-6">
              <div className="text-3xl font-bold text-slate-900 mb-2">10K+</div>
              <div className="text-slate-600">Happy Customers</div>
            </CardContent>
          </Card>
          <Card className="text-center">
            <CardContent className="p-6">
              <div className="text-3xl font-bold text-slate-900 mb-2">500+</div>
              <div className="text-slate-600">Premium Products</div>
            </CardContent>
          </Card>
          <Card className="text-center">
            <CardContent className="p-6">
              <div className="text-3xl font-bold text-slate-900 mb-2">4.9★</div>
              <div className="text-slate-600">Average Rating</div>
            </CardContent>
          </Card>
          <Card className="text-center">
            <CardContent className="p-6">
              <div className="text-3xl font-bold text-slate-900 mb-2">24/7</div>
              <div className="text-slate-600">Customer Support</div>
            </CardContent>
          </Card>
        </div>

        {/* Values Section */}
        <div className="mb-20">
          <h2 className="text-3xl font-bold text-slate-900 text-center mb-12">Our Values</h2>
          <div className="grid md:grid-cols-3 gap-8">
            <Card className="text-center">
              <CardContent className="p-8">
                <div className="w-16 h-16 bg-slate-900 rounded-full mx-auto mb-4 flex items-center justify-center">
                  <Award className="h-8 w-8 text-white" />
                </div>
                <h3 className="text-xl font-semibold text-slate-900 mb-3">Quality First</h3>
                <p className="text-slate-600">
                  We curate only the highest quality products from trusted brands and manufacturers.
                </p>
              </CardContent>
            </Card>
            <Card className="text-center">
              <CardContent className="p-8">
                <div className="w-16 h-16 bg-slate-900 rounded-full mx-auto mb-4 flex items-center justify-center">
                  <Globe className="h-8 w-8 text-white" />
                </div>
                <h3 className="text-xl font-semibold text-slate-900 mb-3">Innovation</h3>
                <p className="text-slate-600">
                  We continuously push boundaries with new technologies to enhance the shopping experience.
                </p>
              </CardContent>
            </Card>
            <Card className="text-center">
              <CardContent className="p-8">
                <div className="w-16 h-16 bg-slate-900 rounded-full mx-auto mb-4 flex items-center justify-center">
                  <Users className="h-8 w-8 text-white" />
                </div>
                <h3 className="text-xl font-semibold text-slate-900 mb-3">Community</h3>
                <p className="text-slate-600">
                  We build lasting relationships with our customers and support the communities we serve.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center bg-gradient-to-br from-slate-50 to-white rounded-2xl p-12">
          <h2 className="text-3xl font-bold text-slate-900 mb-4">Ready to Experience the Future?</h2>
          <p className="text-lg text-slate-600 mb-8 max-w-2xl mx-auto">
            Join thousands of satisfied customers who have discovered the difference that premium products 
            and innovative technology can make.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="bg-slate-900 hover:bg-slate-800">
              Start Shopping
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
            <Button variant="outline" size="lg">
              Contact Us
            </Button>
          </div>
        </div>
      </div>
    </main>
  )
}


