import { MetadataRoute } from "next"

export default function manifest(): MetadataRoute.Manifest {
  return {
    name: "ModernStore - Premium eCommerce Experience",
    short_name: "ModernStore",
    description: "Discover premium products with immersive 3D views and seamless shopping experience",
    start_url: "/",
    display: "standalone",
    background_color: "#ffffff",
    theme_color: "#1e293b",
    orientation: "portrait-primary",
    scope: "/",
    lang: "en-US",
    categories: ["shopping", "lifestyle", "business"],
    icons: [
      {
        src: "/icon-192x192.png",
        sizes: "192x192",
        type: "image/png",
        purpose: "maskable"
      },
      {
        src: "/icon-512x512.png",
        sizes: "512x512",
        type: "image/png",
        purpose: "maskable"
      },
      {
        src: "/icon-192x192.png",
        sizes: "192x192",
        type: "image/png",
        purpose: "any"
      },
      {
        src: "/icon-512x512.png",
        sizes: "512x512",
        type: "image/png",
        purpose: "any"
      }
    ],
    screenshots: [
      {
        src: "/screenshot-mobile.png",
        sizes: "390x844",
        type: "image/png",
        form_factor: "narrow"
      },
      {
        src: "/screenshot-desktop.png",
        sizes: "1920x1080",
        type: "image/png",
        form_factor: "wide"
      }
    ],
    shortcuts: [
      {
        name: "Browse Products",
        short_name: "Products",
        description: "Browse our product catalog",
        url: "/products",
        icons: [
          {
            src: "/shortcut-products.png",
            sizes: "96x96",
            type: "image/png"
          }
        ]
      },
      {
        name: "View Cart",
        short_name: "Cart",
        description: "View your shopping cart",
        url: "/cart",
        icons: [
          {
            src: "/shortcut-cart.png",
            sizes: "96x96",
            type: "image/png"
          }
        ]
      },
      {
        name: "Wishlist",
        short_name: "Wishlist",
        description: "View your saved items",
        url: "/wishlist",
        icons: [
          {
            src: "/shortcut-wishlist.png",
            sizes: "96x96",
            type: "image/png"
          }
        ]
      }
    ]
  }
}
