"use client"

import Link from "next/link"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { useCart } from "@/components/cart-context"

export default function CartPage() {
  const { items, subtotal, updateQuantity, removeItem, clear } = useCart()

  return (
    <main className="py-12">
      <div className="container mx-auto px-4">
        <h1 className="text-3xl font-bold text-slate-900 mb-8">Your Cart</h1>

        {items.length === 0 ? (
          <div className="text-center py-16">
            <p className="text-slate-600 mb-6">Your cart is empty.</p>
            <Link href="/products">
              <Button>Browse Products</Button>
            </Link>
          </div>
        ) : (
          <div className="grid lg:grid-cols-3 gap-8">
            <div className="lg:col-span-2 space-y-4">
              {items.map((it) => (
                <Card key={it.productId}>
                  <CardContent className="p-4 flex items-center gap-4">
                    <img src={it.image || "/placeholder.svg"} alt={it.name} className="w-20 h-20 object-cover rounded" />
                    <div className="flex-1">
                      <Link href={`/products/${it.slug}`} className="font-medium text-slate-900 hover:underline">
                        {it.name}
                      </Link>
                      <div className="text-sm text-slate-600">${it.price.toFixed(2)} each</div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button variant="outline" size="sm" onClick={() => updateQuantity(it.productId, it.quantity - 1)}>-</Button>
                      <input
                        className="w-12 text-center border rounded h-9"
                        type="number"
                        min={1}
                        value={it.quantity}
                        onChange={(e) => updateQuantity(it.productId, Math.max(1, Number(e.target.value) || 1))}
                      />
                      <Button variant="outline" size="sm" onClick={() => updateQuantity(it.productId, it.quantity + 1)}>+</Button>
                    </div>
                    <div className="w-24 text-right font-medium">${(it.price * it.quantity).toFixed(2)}</div>
                    <Button variant="ghost" onClick={() => removeItem(it.productId)}>Remove</Button>
                  </CardContent>
                </Card>
              ))}
            </div>
            <Card>
              <CardContent className="p-6 space-y-4">
                <div className="flex justify-between">
                  <span className="text-slate-600">Subtotal</span>
                  <span className="font-semibold">${subtotal.toFixed(2)}</span>
                </div>
                <div className="text-sm text-slate-500">Taxes and shipping calculated at checkout.</div>
                <Button className="w-full">Checkout</Button>
                <Button variant="outline" className="w-full" onClick={clear}>Clear Cart</Button>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </main>
  )
}



