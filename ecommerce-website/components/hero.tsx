"use client"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ArrowRight, Play } from "lucide-react"
import { useEffect, useRef } from "react"

export function Hero() {
  // Refs to target DOM nodes for animations
  const headlineRef = useRef<HTMLHeadingElement | null>(null)
  const typeContainerRef = useRef<HTMLSpanElement | null>(null)
  const gradientWordRef = useRef<HTMLSpanElement | null>(null)
  const subheadingRef = useRef<HTMLParagraphElement | null>(null)
  const primaryCtaRef = useRef<HTMLButtonElement | null>(null)
  const imageWrapperRef = useRef<HTMLDivElement | null>(null)
  const decoSvgRef = useRef<SVGSVGElement | null>(null)
  const customersRef = useRef<HTMLDivElement | null>(null)
  const productsRef = useRef<HTMLDivElement | null>(null)
  const ratingRef = useRef<HTMLDivElement | null>(null)

  useEffect(() => {
    let isMounted = true
    ;(async () => {
      const prefersReducedMotion = typeof window !== "undefined" && window.matchMedia("(prefers-reduced-motion: reduce)").matches

      // If reduced motion is requested, set final states synchronously and skip animations
      if (prefersReducedMotion) {
        const baseText = "Experience Products in"
        if (typeContainerRef.current) {
          typeContainerRef.current.textContent = baseText
        }
        if (gradientWordRef.current) {
          gradientWordRef.current.style.opacity = "1"
          gradientWordRef.current.style.transform = "translateY(0)"
        }
        if (subheadingRef.current) {
          subheadingRef.current.style.opacity = "1"
          subheadingRef.current.style.transform = "translateY(0)"
        }
        if (imageWrapperRef.current) {
          imageWrapperRef.current.style.opacity = "1"
          imageWrapperRef.current.style.transform = "translateX(0)"
        }
        if (decoSvgRef.current) {
          const paths = decoSvgRef.current.querySelectorAll("path, circle, line")
          paths.forEach((p) => {
            ;(p as SVGElement).setAttribute("stroke-dasharray", "0")
            ;(p as SVGElement).setAttribute("stroke-dashoffset", "0")
          })
        }
        if (customersRef.current) customersRef.current.textContent = "10K+"
        if (productsRef.current) productsRef.current.textContent = "500+"
        if (ratingRef.current) ratingRef.current.textContent = "4.9★"
        return
      }

      const mod = await import("animejs")
      const anime: any = (mod as any).default ?? mod

      if (!isMounted) return

      // 1) Typewriter effect for main headline (first phrase only for readability)
      const baseText = "Experience Products in"
      if (typeContainerRef.current) {
        const typeContainer = typeContainerRef.current
        // initialize with character spans
        typeContainer.innerHTML = ""
        baseText.split("").forEach((ch) => {
          const span = document.createElement("span")
          span.textContent = ch
          span.style.opacity = "0"
          typeContainer.appendChild(span)
        })

        // Hide gradient phrase before reveal
        if (gradientWordRef.current) {
          gradientWordRef.current.style.opacity = "0"
        }

        anime({
          targets: Array.from(typeContainer.children),
          opacity: [0, 1],
          translateY: ["0.25rem", 0],
          easing: "easeInOutSine",
          duration: 22,
          delay: anime.stagger(45),
          complete: () => {
            // Reveal the gradient word after typing finishes
            if (gradientWordRef.current) {
              anime({
                targets: gradientWordRef.current,
                opacity: [0, 1],
                translateY: ["0.5rem", 0],
                easing: "easeInOutSine",
                duration: 450,
              })
            }

            // 2) Staggered fade-in + slide-up for subheading
            if (subheadingRef.current) {
              subheadingRef.current.style.opacity = "0"
              anime({
                targets: subheadingRef.current,
                opacity: [0, 1],
                translateY: ["0.75rem", 0],
                easing: "easeInOutSine",
                duration: 500,
                delay: 120,
              })
            }

            // 3) Subtle continuous pulse for primary CTA
            if (primaryCtaRef.current) {
              primaryCtaRef.current.classList.add("hero-cta-pulse")
            }

            // 4) Image slide-in from right + fade (400ms)
            if (imageWrapperRef.current) {
              imageWrapperRef.current.style.opacity = "0"
              imageWrapperRef.current.style.transform = "translateX(24px)"
              anime({
                targets: imageWrapperRef.current,
                opacity: [0, 1],
                translateX: [24, 0],
                easing: "easeInOutSine",
                duration: 400,
                delay: 100,
              })
            }

            // 5) Decorative SVG stroke-draw
            if (decoSvgRef.current) {
              const paths = decoSvgRef.current.querySelectorAll("path, circle, line")
              paths.forEach((p) => {
                const length = (p as SVGGeometryElement).getTotalLength?.() || 120
                ;(p as SVGElement).setAttribute("stroke-dasharray", `${length}`)
                ;(p as SVGElement).setAttribute("stroke-dashoffset", `${length}`)
              })
              anime({
                targets: paths,
                strokeDashoffset: [anime.setDashoffset, 0],
                easing: "easeInOutSine",
                duration: 800,
                delay: anime.stagger(90),
              })
            }

            // 6) Count-up stats when they enter viewport (once)
            const observer = new IntersectionObserver(
              (entries) => {
                const entry = entries[0]
                if (!entry.isIntersecting) return
                observer.disconnect()

                // Customers: 0 -> 10000 shown as 10K+
                if (customersRef.current) {
                  const counter = { val: 0 }
                  anime({
                    targets: counter,
                    val: 10000,
                    duration: 1200,
                    easing: "easeInOutSine",
                    round: 1,
                    update: () => {
                      const nf = new Intl.NumberFormat(undefined, { notation: "compact", maximumFractionDigits: 1 })
                      customersRef.current!.textContent = `${nf.format(counter.val)}+`
                    },
                  })
                }

                // Products: 0 -> 500 shown as 500+
                if (productsRef.current) {
                  const counter = { val: 0 }
                  anime({
                    targets: counter,
                    val: 500,
                    duration: 1000,
                    easing: "easeInOutSine",
                    round: 1,
                    update: () => {
                      productsRef.current!.textContent = `${Math.round(counter.val)}+`
                    },
                  })
                }

                // Rating: 0 -> 4.9 shown as 4.9★
                if (ratingRef.current) {
                  const counter = { val: 0 }
                  anime({
                    targets: counter,
                    val: 4.9,
                    duration: 900,
                    easing: "easeInOutSine",
                    update: () => {
                      ratingRef.current!.textContent = `${counter.val.toFixed(1)}★`
                    },
                  })
                }
              },
              { threshold: 0.3 }
            )

            // Observe the stats row container (fallback to subheading if missing)
            const statsAnchor = subheadingRef.current?.parentElement?.nextElementSibling as HTMLElement | null
            if (statsAnchor) observer.observe(statsAnchor)
          },
        })
      }
    })()

    return () => {
      isMounted = false
    }
  }, [])

  return (
    <section className="relative overflow-hidden min-h-screen">
      {/* Background Image */}
      <div
        className="absolute inset-0 bg-cover bg-center bg-no-repeat"
        style={{
          backgroundImage: "url('/3d-product-showcase-hero-image.jpg')",
        }}
      >
        {/* Overlay for better text readability */}
        <div className="absolute inset-0 bg-gradient-to-r from-slate-900/80 via-slate-900/60 to-slate-900/40" />
        <div className="absolute inset-0 bg-gradient-to-b from-transparent via-transparent to-slate-900/20" />
      </div>

      {/* Decorative SVG lines */}
      <svg
        ref={decoSvgRef}
        className="pointer-events-none absolute -top-10 -left-10 h-64 w-64 opacity-20 text-white/30 z-10"
        viewBox="0 0 200 200"
        fill="none"
        stroke="currentColor"
        strokeWidth="1.5"
        aria-hidden="true"
      >
        <path d="M10 10 C 60 40, 140 40, 190 10" />
        <path d="M10 60 C 60 90, 140 90, 190 60" />
        <circle cx="100" cy="140" r="30" />
      </svg>

      <div className="relative z-10 container mx-auto px-4 py-24 lg:py-32 min-h-screen flex items-center">
        <div className="grid lg:grid-cols-2 gap-12 items-center w-full">
          <div className="space-y-8">
            <div className="space-y-4">
              <h1 ref={headlineRef} className="text-4xl lg:text-6xl font-bold text-white text-balance">
                {/* Screen-reader friendly static content */}
                <span className="sr-only">Experience Products in 3D Reality</span>
                {/* Typewriter visible content */}
                <span ref={typeContainerRef} aria-hidden="true" className="inline-block align-top" />
                {" "}
                {/* Gradient key phrase is revealed after typing completes. */}
                <span ref={gradientWordRef} className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent inline-block">
                  3D Reality
                </span>
              </h1>
              <p ref={subheadingRef} className="text-xl text-slate-200 text-pretty max-w-lg">
                Discover premium products with immersive 3D views, seamless shopping, and exceptional quality. The
                future of online shopping is here.
              </p>
            </div>

            <div className="flex flex-col sm:flex-row gap-4">
              <Button ref={primaryCtaRef} size="lg" className="bg-white hover:bg-slate-100 text-slate-900 font-semibold">
                Shop Now
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
              <Button variant="outline" size="lg" className="border-white/30 bg-white/10 backdrop-blur-sm text-white hover:bg-white/20">
                <Play className="mr-2 h-4 w-4" />
                Watch Demo
              </Button>
            </div>

            <div className="flex items-center space-x-8 pt-8">
              <div className="text-center">
                <div ref={customersRef} className="text-2xl font-bold text-white">10K+</div>
                <div className="text-sm text-slate-300">Happy Customers</div>
              </div>
              <div className="text-center">
                <div ref={productsRef} className="text-2xl font-bold text-white">500+</div>
                <div className="text-sm text-slate-300">Premium Products</div>
              </div>
              <div className="text-center">
                <div ref={ratingRef} className="text-2xl font-bold text-white">4.9★</div>
                <div className="text-sm text-slate-300">Customer Rating</div>
              </div>
            </div>
          </div>

          {/* Feature highlights on the right side */}
          <div className="relative lg:flex hidden">
            <div ref={imageWrapperRef} className="space-y-6 will-change-transform">
              {/* Feature cards floating over the background */}
              <div className="bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20">
                <div className="flex items-center space-x-3 mb-4">
                  <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse" />
                  <span className="text-sm font-medium text-white">Live 3D Preview</span>
                </div>
                <h3 className="text-lg font-semibold text-white mb-2">Interactive 3D Models</h3>
                <p className="text-slate-300 text-sm">Rotate, zoom, and explore products in stunning detail</p>
              </div>

              <div className="bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20">
                <div className="flex items-center space-x-3 mb-4">
                  <div className="w-3 h-3 bg-blue-400 rounded-full animate-pulse" />
                  <span className="text-sm font-medium text-white">Smart Search</span>
                </div>
                <h3 className="text-lg font-semibold text-white mb-2">AI-Powered Discovery</h3>
                <p className="text-slate-300 text-sm">Find exactly what you're looking for with intelligent filters</p>
              </div>

              <div className="bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20">
                <div className="flex items-center space-x-3 mb-4">
                  <div className="w-3 h-3 bg-purple-400 rounded-full animate-pulse" />
                  <span className="text-sm font-medium text-white">Premium Quality</span>
                </div>
                <h3 className="text-lg font-semibold text-white mb-2">Curated Collection</h3>
                <p className="text-slate-300 text-sm">Hand-picked products from trusted brands worldwide</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Scroll indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-10">
        <div className="flex flex-col items-center space-y-2 text-white/70 animate-bounce">
          <span className="text-sm font-medium">Scroll to explore</span>
          <div className="w-6 h-10 border-2 border-white/30 rounded-full flex justify-center">
            <div className="w-1 h-3 bg-white/50 rounded-full mt-2 animate-pulse" />
          </div>
        </div>
      </div>

      {/* Local, scoped keyframes for the CTA pulse (GPU-friendly) */}
      <style jsx>{`
        @keyframes heroCtaPulse {
          0% { transform: scale(1); }
          50% { transform: scale(1.03); }
          100% { transform: scale(1); }
        }
        .hero-cta-pulse {
          animation: heroCtaPulse 2.2s ease-in-out infinite;
          will-change: transform;
        }
        @media (prefers-reduced-motion: reduce) {
          .hero-cta-pulse { animation: none; }
        }
      `}</style>
    </section>
  )
}
