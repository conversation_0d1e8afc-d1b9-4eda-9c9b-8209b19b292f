"use client"

import { createContext, useContext, useState, useEffect, ReactNode } from "react"
import type { Product } from "@/lib/types"

interface WishlistItem {
  productId: string
  product: Product
  addedAt: Date
}

interface WishlistContextValue {
  items: WishlistItem[]
  itemCount: number
  addItem: (product: Product) => void
  removeItem: (productId: string) => void
  isInWishlist: (productId: string) => boolean
  clear: () => void
  toggleItem: (product: Product) => void
}

const WishlistContext = createContext<WishlistContextValue | undefined>(undefined)

const STORAGE_KEY = "modernstore.wishlist.v1"

export function WishlistProvider({ children }: { children: ReactNode }) {
  const [items, setItems] = useState<WishlistItem[]>([])

  // Load wishlist from localStorage on mount
  useEffect(() => {
    try {
      const raw = typeof window !== "undefined" ? window.localStorage.getItem(STORAGE_KEY) : null
      if (raw) {
        const parsed = JSON.parse(raw) as WishlistItem[]
        // Convert date strings back to Date objects
        const itemsWithDates = parsed.map(item => ({
          ...item,
          addedAt: new Date(item.addedAt)
        }))
        setItems(itemsWithDates)
      }
    } catch (error) {
      console.error("Failed to load wishlist from localStorage:", error)
    }
  }, [])

  // Save wishlist to localStorage whenever it changes
  useEffect(() => {
    try {
      if (typeof window !== "undefined") {
        window.localStorage.setItem(STORAGE_KEY, JSON.stringify(items))
      }
    } catch (error) {
      console.error("Failed to save wishlist to localStorage:", error)
    }
  }, [items])

  const addItem = (product: Product) => {
    setItems(prev => {
      // Check if item already exists
      if (prev.some(item => item.productId === product.id)) {
        return prev
      }
      
      const newItem: WishlistItem = {
        productId: product.id,
        product,
        addedAt: new Date()
      }
      
      return [...prev, newItem]
    })
  }

  const removeItem = (productId: string) => {
    setItems(prev => prev.filter(item => item.productId !== productId))
  }

  const isInWishlist = (productId: string) => {
    return items.some(item => item.productId === productId)
  }

  const clear = () => {
    setItems([])
  }

  const toggleItem = (product: Product) => {
    if (isInWishlist(product.id)) {
      removeItem(product.id)
    } else {
      addItem(product)
    }
  }

  const value: WishlistContextValue = {
    items,
    itemCount: items.length,
    addItem,
    removeItem,
    isInWishlist,
    clear,
    toggleItem
  }

  return <WishlistContext.Provider value={value}>{children}</WishlistContext.Provider>
}

export function useWishlist() {
  const context = useContext(WishlistContext)
  if (context === undefined) {
    throw new Error("useWishlist must be used within a WishlistProvider")
  }
  return context
}
