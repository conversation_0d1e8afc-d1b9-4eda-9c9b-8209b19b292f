import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { ArrowRight } from "lucide-react"

const categories = [
  {
    id: 1,
    name: "Electronics",
    description: "Latest tech gadgets and devices",
    image: "/electronics-category.png",
    productCount: 150,
  },
  {
    id: 2,
    name: "Fashion",
    description: "Trendy clothing and accessories",
    image: "/fashion-category.png",
    productCount: 200,
  },
  {
    id: 3,
    name: "Home & Garden",
    description: "Beautiful home decor and garden essentials",
    image: "/home-garden-category.png",
    productCount: 120,
  },
  {
    id: 4,
    name: "Sports & Fitness",
    description: "Equipment for active lifestyle",
    image: "/sports-fitness-category.jpg",
    productCount: 80,
  },
  {
    id: 5,
    name: "Books & Media",
    description: "Knowledge and entertainment",
    image: "/books-media-category.jpg",
    productCount: 300,
  },
  {
    id: 6,
    name: "Health & Beauty",
    description: "Wellness and beauty products",
    image: "/health-beauty-category.jpg",
    productCount: 90,
  },
]

export function CategoryGrid() {
  return (
    <section className="py-16 bg-slate-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl lg:text-4xl font-bold text-slate-900 mb-4">Shop by Category</h2>
          <p className="text-lg text-slate-600 max-w-2xl mx-auto">
            Explore our diverse range of categories and find exactly what you're looking for
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {categories.map((category) => (
            <Card key={category.id} className="group hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
              <CardContent className="p-0">
                <div className="relative overflow-hidden rounded-t-lg">
                  <img
                    src={category.image || "/placeholder.svg"}
                    alt={category.name}
                    className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  <div className="absolute inset-0 bg-black/20 group-hover:bg-black/30 transition-colors" />
                  <div className="absolute bottom-4 left-4 text-white">
                    <h3 className="text-xl font-bold mb-1">{category.name}</h3>
                    <p className="text-sm opacity-90">{category.productCount} products</p>
                  </div>
                </div>

                <div className="p-6">
                  <p className="text-slate-600 mb-4">{category.description}</p>
                  <Button
                    variant="outline"
                    className="w-full group-hover:bg-slate-900 group-hover:text-white transition-colors bg-transparent"
                  >
                    Explore Category
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  )
}
