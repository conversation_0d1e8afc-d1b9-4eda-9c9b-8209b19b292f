"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { <PERSON>, <PERSON>Off, Settings } from "lucide-react"
import { toast } from "sonner"

interface NotificationSettings {
  orderUpdates: boolean
  promotions: boolean
  newProducts: boolean
  priceDrops: boolean
}

export function PushNotifications() {
  const [isSupported, setIsSupported] = useState(false)
  const [permission, setPermission] = useState<NotificationPermission>('default')
  const [isSubscribed, setIsSubscribed] = useState(false)
  const [settings, setSettings] = useState<NotificationSettings>({
    orderUpdates: true,
    promotions: false,
    newProducts: false,
    priceDrops: false
  })

  useEffect(() => {
    // Check if notifications are supported
    if ('Notification' in window && 'serviceWorker' in navigator) {
      setIsSupported(true)
      setPermission(Notification.permission)
      
      // Load saved settings
      const savedSettings = localStorage.getItem('notification-settings')
      if (savedSettings) {
        setSettings(JSON.parse(savedSettings))
      }
      
      // Check if already subscribed
      checkSubscription()
    }
  }, [])

  const checkSubscription = async () => {
    try {
      const registration = await navigator.serviceWorker.ready
      const subscription = await registration.pushManager.getSubscription()
      setIsSubscribed(!!subscription)
    } catch (error) {
      console.error('Error checking subscription:', error)
    }
  }

  const requestPermission = async () => {
    try {
      const permission = await Notification.requestPermission()
      setPermission(permission)
      
      if (permission === 'granted') {
        await subscribeToNotifications()
        toast.success("Notifications enabled!", {
          description: "You'll receive updates about your orders and promotions."
        })
      } else {
        toast.error("Notifications blocked", {
          description: "You can enable them later in your browser settings."
        })
      }
    } catch (error) {
      console.error('Error requesting permission:', error)
      toast.error("Failed to enable notifications")
    }
  }

  const subscribeToNotifications = async () => {
    try {
      const registration = await navigator.serviceWorker.ready
      
      // In a real app, you would get this from your server
      const vapidPublicKey = 'your-vapid-public-key'
      
      const subscription = await registration.pushManager.subscribe({
        userVisibleOnly: true,
        applicationServerKey: urlBase64ToUint8Array(vapidPublicKey)
      })
      
      // Send subscription to your server
      await sendSubscriptionToServer(subscription)
      setIsSubscribed(true)
    } catch (error) {
      console.error('Error subscribing to notifications:', error)
      toast.error("Failed to subscribe to notifications")
    }
  }

  const unsubscribeFromNotifications = async () => {
    try {
      const registration = await navigator.serviceWorker.ready
      const subscription = await registration.pushManager.getSubscription()
      
      if (subscription) {
        await subscription.unsubscribe()
        // Remove subscription from your server
        await removeSubscriptionFromServer(subscription)
        setIsSubscribed(false)
        toast.success("Notifications disabled")
      }
    } catch (error) {
      console.error('Error unsubscribing from notifications:', error)
      toast.error("Failed to unsubscribe from notifications")
    }
  }

  const updateSettings = (key: keyof NotificationSettings, value: boolean) => {
    const newSettings = { ...settings, [key]: value }
    setSettings(newSettings)
    localStorage.setItem('notification-settings', JSON.stringify(newSettings))
    
    // In a real app, you would send these preferences to your server
    toast.success("Notification preferences updated")
  }

  const sendTestNotification = () => {
    if (permission === 'granted') {
      new Notification('ModernStore Test', {
        body: 'This is a test notification from ModernStore!',
        icon: '/icon-192x192.png',
        badge: '/icon-192x192.png'
      })
    }
  }

  if (!isSupported) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <BellOff className="h-12 w-12 text-slate-400 mx-auto mb-4" />
          <p className="text-slate-600">
            Push notifications are not supported in your browser.
          </p>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Main Notification Toggle */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bell className="h-5 w-5" />
            Push Notifications
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <Label className="text-base font-medium">Enable Notifications</Label>
                <p className="text-sm text-slate-600">
                  Get updates about your orders and special offers
                </p>
              </div>
              <Switch
                checked={permission === 'granted' && isSubscribed}
                onCheckedChange={(checked) => {
                  if (checked) {
                    requestPermission()
                  } else {
                    unsubscribeFromNotifications()
                  }
                }}
                disabled={permission === 'denied'}
              />
            </div>

            {permission === 'denied' && (
              <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                <p className="text-sm text-yellow-800">
                  Notifications are blocked. Please enable them in your browser settings.
                </p>
              </div>
            )}

            {permission === 'granted' && isSubscribed && (
              <Button
                variant="outline"
                size="sm"
                onClick={sendTestNotification}
              >
                Send Test Notification
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Notification Preferences */}
      {permission === 'granted' && isSubscribed && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Notification Preferences
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <Label>Order Updates</Label>
                  <p className="text-sm text-slate-600">
                    Shipping confirmations and delivery updates
                  </p>
                </div>
                <Switch
                  checked={settings.orderUpdates}
                  onCheckedChange={(checked) => updateSettings('orderUpdates', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <Label>Promotions & Sales</Label>
                  <p className="text-sm text-slate-600">
                    Special offers and discount notifications
                  </p>
                </div>
                <Switch
                  checked={settings.promotions}
                  onCheckedChange={(checked) => updateSettings('promotions', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <Label>New Products</Label>
                  <p className="text-sm text-slate-600">
                    Alerts about new product launches
                  </p>
                </div>
                <Switch
                  checked={settings.newProducts}
                  onCheckedChange={(checked) => updateSettings('newProducts', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <Label>Price Drops</Label>
                  <p className="text-sm text-slate-600">
                    Notifications when wishlist items go on sale
                  </p>
                </div>
                <Switch
                  checked={settings.priceDrops}
                  onCheckedChange={(checked) => updateSettings('priceDrops', checked)}
                />
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}

// Helper functions
function urlBase64ToUint8Array(base64String: string): Uint8Array {
  const padding = '='.repeat((4 - base64String.length % 4) % 4)
  const base64 = (base64String + padding)
    .replace(/-/g, '+')
    .replace(/_/g, '/')

  const rawData = window.atob(base64)
  const outputArray = new Uint8Array(rawData.length)

  for (let i = 0; i < rawData.length; ++i) {
    outputArray[i] = rawData.charCodeAt(i)
  }
  return outputArray
}

async function sendSubscriptionToServer(subscription: PushSubscription) {
  // In a real app, send this to your backend
  console.log('Sending subscription to server:', subscription)
  
  // Example API call:
  // await fetch('/api/notifications/subscribe', {
  //   method: 'POST',
  //   headers: { 'Content-Type': 'application/json' },
  //   body: JSON.stringify(subscription)
  // })
}

async function removeSubscriptionFromServer(subscription: PushSubscription) {
  // In a real app, remove this from your backend
  console.log('Removing subscription from server:', subscription)
  
  // Example API call:
  // await fetch('/api/notifications/unsubscribe', {
  //   method: 'POST',
  //   headers: { 'Content-Type': 'application/json' },
  //   body: JSON.stringify(subscription)
  // })
}
