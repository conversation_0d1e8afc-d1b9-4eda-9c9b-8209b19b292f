"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Heart } from "lucide-react"
import { useWishlist } from "@/components/wishlist-context"
import { toast } from "sonner"
import type { Product } from "@/lib/types"

interface WishlistButtonProps {
  product: Product
  variant?: "default" | "outline" | "ghost"
  size?: "sm" | "default" | "lg"
  className?: string
  showText?: boolean
}

export function WishlistButton({ 
  product, 
  variant = "ghost", 
  size = "sm", 
  className = "",
  showText = false
}: WishlistButtonProps) {
  const { isInWishlist, toggleItem } = useWishlist()
  const inWishlist = isInWishlist(product.id)

  const handleClick = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    
    toggleItem(product)
    
    if (inWishlist) {
      toast.success("Removed from wishlist", {
        description: product.name
      })
    } else {
      toast.success("Added to wishlist", {
        description: product.name
      })
    }
  }

  return (
    <Button
      variant={variant}
      size={size}
      onClick={handleClick}
      className={`transition-colors ${className}`}
      title={inWishlist ? "Remove from wishlist" : "Add to wishlist"}
    >
      <Heart 
        className={`h-4 w-4 transition-colors ${
          inWishlist 
            ? "text-red-500 fill-red-500" 
            : "text-slate-600 hover:text-red-500"
        } ${showText ? "mr-2" : ""}`}
      />
      {showText && (
        <span className="hidden sm:inline">
          {inWishlist ? "Remove from Wishlist" : "Add to Wishlist"}
        </span>
      )}
    </Button>
  )
}
