import Head from "next/head"

interface SEOProps {
  title?: string
  description?: string
  keywords?: string[]
  image?: string
  url?: string
  type?: "website" | "article" | "product"
  price?: number
  currency?: string
  availability?: "in_stock" | "out_of_stock" | "preorder"
  brand?: string
  category?: string
}

export function SEO({
  title = "ModernStore - Premium eCommerce Experience",
  description = "Discover premium products with immersive 3D views and seamless shopping experience",
  keywords = ["ecommerce", "online shopping", "premium products", "3D product view"],
  image = "/3d-product-showcase-hero-image.jpg",
  url = "https://modernstore.com",
  type = "website",
  price,
  currency = "USD",
  availability = "in_stock",
  brand = "ModernStore",
  category
}: SEOProps) {
  const fullTitle = title.includes("ModernStore") ? title : `${title} | ModernStore`
  const fullUrl = url.startsWith("http") ? url : `https://modernstore.com${url}`
  const fullImage = image.startsWith("http") ? image : `https://modernstore.com${image}`

  const structuredData = {
    "@context": "https://schema.org",
    "@type": type === "product" ? "Product" : "WebSite",
    name: title,
    description,
    image: fullImage,
    url: fullUrl,
    ...(type === "product" && {
      brand: {
        "@type": "Brand",
        name: brand
      },
      offers: {
        "@type": "Offer",
        price: price?.toString(),
        priceCurrency: currency,
        availability: `https://schema.org/${availability === "in_stock" ? "InStock" : "OutOfStock"}`,
        url: fullUrl
      },
      ...(category && {
        category: {
          "@type": "ProductCategory",
          name: category
        }
      })
    }),
    ...(type === "website" && {
      potentialAction: {
        "@type": "SearchAction",
        target: {
          "@type": "EntryPoint",
          urlTemplate: "https://modernstore.com/products?search={search_term_string}"
        },
        "query-input": "required name=search_term_string"
      }
    })
  }

  return (
    <Head>
      {/* Basic Meta Tags */}
      <title>{fullTitle}</title>
      <meta name="description" content={description} />
      <meta name="keywords" content={keywords.join(", ")} />
      <meta name="author" content="ModernStore" />
      <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      <meta name="robots" content="index, follow" />
      <link rel="canonical" href={fullUrl} />

      {/* Open Graph Meta Tags */}
      <meta property="og:type" content={type} />
      <meta property="og:title" content={fullTitle} />
      <meta property="og:description" content={description} />
      <meta property="og:image" content={fullImage} />
      <meta property="og:url" content={fullUrl} />
      <meta property="og:site_name" content="ModernStore" />
      <meta property="og:locale" content="en_US" />

      {/* Twitter Card Meta Tags */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={fullTitle} />
      <meta name="twitter:description" content={description} />
      <meta name="twitter:image" content={fullImage} />
      <meta name="twitter:site" content="@modernstore" />
      <meta name="twitter:creator" content="@modernstore" />

      {/* Product-specific meta tags */}
      {type === "product" && price && (
        <>
          <meta property="product:price:amount" content={price.toString()} />
          <meta property="product:price:currency" content={currency} />
          <meta property="product:availability" content={availability} />
          {brand && <meta property="product:brand" content={brand} />}
          {category && <meta property="product:category" content={category} />}
        </>
      )}

      {/* Favicon and App Icons */}
      <link rel="icon" type="image/x-icon" href="/favicon.ico" />
      <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
      <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
      <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
      <link rel="manifest" href="/site.webmanifest" />
      <meta name="theme-color" content="#1e293b" />

      {/* Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(structuredData)
        }}
      />

      {/* Preconnect to external domains */}
      <link rel="preconnect" href="https://fonts.googleapis.com" />
      <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      <link rel="dns-prefetch" href="https://www.google-analytics.com" />
    </Head>
  )
}
