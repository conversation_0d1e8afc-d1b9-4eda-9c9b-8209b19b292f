"use client"

import { useEffect } from "react"
import { toast } from "sonner"

export function ServiceWorkerRegistration() {
  useEffect(() => {
    if (typeof window !== 'undefined' && 'serviceWorker' in navigator) {
      registerServiceWorker()
    }
  }, [])

  const registerServiceWorker = async () => {
    try {
      const registration = await navigator.serviceWorker.register('/sw.js', {
        scope: '/'
      })

      console.log('Service Worker registered successfully:', registration)

      // Handle updates
      registration.addEventListener('updatefound', () => {
        const newWorker = registration.installing
        if (newWorker) {
          newWorker.addEventListener('statechange', () => {
            if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
              // New content is available
              toast.info("App update available", {
                description: "Refresh to get the latest version.",
                action: {
                  label: "Refresh",
                  onClick: () => window.location.reload()
                },
                duration: 10000
              })
            }
          })
        }
      })

      // Handle controller change (when new <PERSON><PERSON> takes control)
      navigator.serviceWorker.addEventListener('controllerchange', () => {
        window.location.reload()
      })

    } catch (error) {
      console.error('Service Worker registration failed:', error)
    }
  }

  return null // This component doesn't render anything
}

// Utility functions for service worker communication
export const swUtils = {
  // Send message to service worker
  sendMessage: async (message: any) => {
    if ('serviceWorker' in navigator && navigator.serviceWorker.controller) {
      navigator.serviceWorker.controller.postMessage(message)
    }
  },

  // Request background sync
  requestSync: async (tag: string) => {
    try {
      const registration = await navigator.serviceWorker.ready
      if ('sync' in registration) {
        await (registration as any).sync.register(tag)
      }
    } catch (error) {
      console.error('Background sync registration failed:', error)
    }
  },

  // Check if app is running in standalone mode (PWA)
  isStandalone: () => {
    return window.matchMedia('(display-mode: standalone)').matches ||
           (window.navigator as any).standalone === true
  },

  // Check if device is online
  isOnline: () => navigator.onLine,

  // Get network information (if available)
  getNetworkInfo: () => {
    const connection = (navigator as any).connection || 
                      (navigator as any).mozConnection || 
                      (navigator as any).webkitConnection
    
    if (connection) {
      return {
        effectiveType: connection.effectiveType,
        downlink: connection.downlink,
        rtt: connection.rtt,
        saveData: connection.saveData
      }
    }
    return null
  }
}
