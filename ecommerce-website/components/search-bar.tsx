"use client"

import { useState, useEffect, useRef } from "react"
import { useRouter } from "next/navigation"
import { Search, X } from "lucide-react"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { useSearch } from "@/components/search-context"
import { mockProducts } from "@/lib/mock-data"
import Link from "next/link"

interface SearchBarProps {
  className?: string
  placeholder?: string
  showSuggestions?: boolean
}

export function SearchBar({ 
  className = "", 
  placeholder = "Search products...", 
  showSuggestions = true 
}: SearchBarProps) {
  const { filters, updateFilters } = useSearch()
  const [localQuery, setLocalQuery] = useState(filters.query)
  const [showDropdown, setShowDropdown] = useState(false)
  const [suggestions, setSuggestions] = useState<typeof mockProducts>([])
  const router = useRouter()
  const searchRef = useRef<HTMLDivElement>(null)

  // Update local query when filters change
  useEffect(() => {
    setLocalQuery(filters.query)
  }, [filters.query])

  // Generate suggestions based on query
  useEffect(() => {
    if (!localQuery.trim() || !showSuggestions) {
      setSuggestions([])
      setShowDropdown(false)
      return
    }

    const query = localQuery.toLowerCase()
    const filtered = mockProducts
      .filter(product =>
        product.name.toLowerCase().includes(query) ||
        product.description.toLowerCase().includes(query) ||
        product.tags.some(tag => tag.toLowerCase().includes(query))
      )
      .slice(0, 5) // Limit to 5 suggestions

    setSuggestions(filtered)
    setShowDropdown(filtered.length > 0)
  }, [localQuery, showSuggestions])

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setShowDropdown(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => document.removeEventListener("mousedown", handleClickOutside)
  }, [])

  const handleSearch = (query: string) => {
    updateFilters({ query })
    setShowDropdown(false)
    
    // Navigate to products page if not already there
    if (window.location.pathname !== "/products") {
      router.push("/products")
    }
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    handleSearch(localQuery)
  }

  const handleClear = () => {
    setLocalQuery("")
    updateFilters({ query: "" })
    setShowDropdown(false)
  }

  const handleSuggestionClick = (productName: string) => {
    setLocalQuery(productName)
    handleSearch(productName)
  }

  return (
    <div ref={searchRef} className={`relative ${className}`}>
      <form onSubmit={handleSubmit} className="relative">
        <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-slate-400" />
        <Input
          type="text"
          placeholder={placeholder}
          value={localQuery}
          onChange={(e) => setLocalQuery(e.target.value)}
          className="pl-10 pr-10 bg-slate-50 border-slate-200 focus:bg-white"
          onFocus={() => {
            if (suggestions.length > 0) setShowDropdown(true)
          }}
        />
        {localQuery && (
          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={handleClear}
            className="absolute right-1 top-1/2 -translate-y-1/2 h-8 w-8 p-0 hover:bg-slate-100"
          >
            <X className="h-4 w-4" />
          </Button>
        )}
      </form>

      {/* Search Suggestions Dropdown */}
      {showDropdown && suggestions.length > 0 && (
        <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-slate-200 rounded-md shadow-lg z-50 max-h-80 overflow-y-auto">
          <div className="p-2">
            <div className="text-xs text-slate-500 mb-2 px-2">Suggestions</div>
            {suggestions.map((product) => (
              <Link
                key={product.id}
                href={`/products/${product.slug}`}
                onClick={() => setShowDropdown(false)}
                className="flex items-center gap-3 p-2 hover:bg-slate-50 rounded-md transition-colors"
              >
                <img
                  src={product.images[0] || "/placeholder.svg"}
                  alt={product.name}
                  className="w-10 h-10 object-cover rounded"
                />
                <div className="flex-1 min-w-0">
                  <div className="font-medium text-sm text-slate-900 truncate">
                    {product.name}
                  </div>
                  <div className="text-xs text-slate-500">
                    ${product.price.toFixed(2)}
                  </div>
                </div>
              </Link>
            ))}
          </div>
          <div className="border-t border-slate-100 p-2">
            <button
              onClick={() => handleSearch(localQuery)}
              className="w-full text-left px-2 py-1 text-sm text-slate-600 hover:text-slate-900 hover:bg-slate-50 rounded transition-colors"
            >
              Search for "{localQuery}"
            </button>
          </div>
        </div>
      )}
    </div>
  )
}
