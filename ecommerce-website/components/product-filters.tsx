"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { Slider } from "@/components/ui/slider"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { X, Filter, RotateCcw } from "lucide-react"
import { useSearch } from "@/components/search-context"
import { mockCategories } from "@/lib/mock-data"

interface ProductFiltersProps {
  className?: string
  isMobile?: boolean
  onClose?: () => void
}

export function ProductFilters({ className = "", isMobile = false, onClose }: ProductFiltersProps) {
  const { filters, updateFilters, resetFilters, filteredProducts } = useSearch()
  const [localPriceRange, setLocalPriceRange] = useState(filters.priceRange)

  const handlePriceRangeChange = (value: number[]) => {
    setLocalPriceRange([value[0], value[1]])
  }

  const handlePriceRangeCommit = (value: number[]) => {
    updateFilters({ priceRange: [value[0], value[1]] })
  }

  const handleCategoryChange = (categorySlug: string) => {
    updateFilters({ 
      category: filters.category === categorySlug ? "" : categorySlug 
    })
  }

  const handleSortChange = (sortBy: string) => {
    updateFilters({ 
      sortBy: sortBy as "name" | "price-low" | "price-high" | "newest" | "featured"
    })
  }

  const handleInStockChange = (checked: boolean) => {
    updateFilters({ inStock: checked })
  }

  const getActiveFiltersCount = () => {
    let count = 0
    if (filters.query) count++
    if (filters.category) count++
    if (filters.priceRange[0] > 0 || filters.priceRange[1] < 1000) count++
    if (filters.inStock) count++
    if (filters.sortBy !== "featured") count++
    return count
  }

  const activeFiltersCount = getActiveFiltersCount()

  return (
    <div className={className}>
      {/* Mobile Header */}
      {isMobile && (
        <div className="flex items-center justify-between p-4 border-b">
          <div className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            <h2 className="font-semibold">Filters</h2>
            {activeFiltersCount > 0 && (
              <Badge variant="secondary" className="ml-2">
                {activeFiltersCount}
              </Badge>
            )}
          </div>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>
      )}

      <div className="space-y-6 p-4">
        {/* Results Count & Reset */}
        <div className="flex items-center justify-between">
          <div className="text-sm text-slate-600">
            {filteredProducts.length} products found
          </div>
          {activeFiltersCount > 0 && (
            <Button
              variant="ghost"
              size="sm"
              onClick={resetFilters}
              className="text-slate-600 hover:text-slate-900"
            >
              <RotateCcw className="h-4 w-4 mr-1" />
              Reset
            </Button>
          )}
        </div>

        {/* Sort By */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm">Sort By</CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            <Select value={filters.sortBy} onValueChange={handleSortChange}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="featured">Featured</SelectItem>
                <SelectItem value="newest">Newest</SelectItem>
                <SelectItem value="name">Name A-Z</SelectItem>
                <SelectItem value="price-low">Price: Low to High</SelectItem>
                <SelectItem value="price-high">Price: High to Low</SelectItem>
              </SelectContent>
            </Select>
          </CardContent>
        </Card>

        {/* Categories */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm">Categories</CardTitle>
          </CardHeader>
          <CardContent className="pt-0 space-y-3">
            {mockCategories.map((category) => (
              <div key={category.id} className="flex items-center space-x-2">
                <Checkbox
                  id={`category-${category.id}`}
                  checked={filters.category === category.slug}
                  onCheckedChange={() => handleCategoryChange(category.slug)}
                />
                <Label
                  htmlFor={`category-${category.id}`}
                  className="text-sm font-normal cursor-pointer"
                >
                  {category.name}
                </Label>
              </div>
            ))}
          </CardContent>
        </Card>

        {/* Price Range */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm">Price Range</CardTitle>
          </CardHeader>
          <CardContent className="pt-0 space-y-4">
            <div className="px-2">
              <Slider
                value={localPriceRange}
                onValueChange={handlePriceRangeChange}
                onValueCommit={handlePriceRangeCommit}
                max={1000}
                min={0}
                step={10}
                className="w-full"
              />
            </div>
            <div className="flex items-center justify-between text-sm text-slate-600">
              <span>${localPriceRange[0]}</span>
              <span>${localPriceRange[1]}</span>
            </div>
          </CardContent>
        </Card>

        {/* Availability */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm">Availability</CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="in-stock"
                checked={filters.inStock}
                onCheckedChange={handleInStockChange}
              />
              <Label htmlFor="in-stock" className="text-sm font-normal cursor-pointer">
                In Stock Only
              </Label>
            </div>
          </CardContent>
        </Card>

        {/* Active Filters */}
        {activeFiltersCount > 0 && (
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm">Active Filters</CardTitle>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="flex flex-wrap gap-2">
                {filters.query && (
                  <Badge variant="secondary" className="text-xs">
                    Search: {filters.query}
                    <Button
                      variant="ghost"
                      size="sm"
                      className="ml-1 h-auto p-0 text-slate-500 hover:text-slate-700"
                      onClick={() => updateFilters({ query: "" })}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </Badge>
                )}
                {filters.category && (
                  <Badge variant="secondary" className="text-xs">
                    {mockCategories.find(c => c.slug === filters.category)?.name}
                    <Button
                      variant="ghost"
                      size="sm"
                      className="ml-1 h-auto p-0 text-slate-500 hover:text-slate-700"
                      onClick={() => updateFilters({ category: "" })}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </Badge>
                )}
                {filters.inStock && (
                  <Badge variant="secondary" className="text-xs">
                    In Stock
                    <Button
                      variant="ghost"
                      size="sm"
                      className="ml-1 h-auto p-0 text-slate-500 hover:text-slate-700"
                      onClick={() => updateFilters({ inStock: false })}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </Badge>
                )}
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}
