"use client"

import { useRef, useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { RotateCcw, Maximize2, Minimize2, Play, Pause, RotateCw, Cube } from "lucide-react"

interface Product3DViewerProps {
  modelUrl?: string
  productName: string
  className?: string
  autoRotate?: boolean
  showControls?: boolean
}

// CSS-based 3D cube animation component (fallback when Three.js isn't available)
function CSS3DCube({ productName, autoRotate }: { productName: string; autoRotate: boolean }) {
  return (
    <div className="flex items-center justify-center h-full">
      <div className="relative w-32 h-32 transform-gpu perspective-1000">
        <div
          className={`relative w-full h-full transform-style-preserve-3d ${
            autoRotate ? 'animate-spin-slow' : ''
          }`}
          style={{
            animation: autoRotate ? 'spin3d 8s linear infinite' : 'none'
          }}
        >
          {/* Cube faces */}
          <div className="absolute w-full h-full bg-gradient-to-br from-slate-400 to-slate-600 border border-slate-300 flex items-center justify-center text-white text-xs font-medium transform translate-z-16">
            Front
          </div>
          <div className="absolute w-full h-full bg-gradient-to-br from-slate-500 to-slate-700 border border-slate-300 flex items-center justify-center text-white text-xs font-medium transform rotate-y-90 translate-z-16">
            Right
          </div>
          <div className="absolute w-full h-full bg-gradient-to-br from-slate-600 to-slate-800 border border-slate-300 flex items-center justify-center text-white text-xs font-medium transform rotate-y-180 translate-z-16">
            Back
          </div>
          <div className="absolute w-full h-full bg-gradient-to-br from-slate-500 to-slate-700 border border-slate-300 flex items-center justify-center text-white text-xs font-medium transform rotate-y-270 translate-z-16">
            Left
          </div>
          <div className="absolute w-full h-full bg-gradient-to-br from-slate-300 to-slate-500 border border-slate-300 flex items-center justify-center text-white text-xs font-medium transform rotate-x-90 translate-z-16">
            Top
          </div>
          <div className="absolute w-full h-full bg-gradient-to-br from-slate-700 to-slate-900 border border-slate-300 flex items-center justify-center text-white text-xs font-medium transform rotate-x-270 translate-z-16">
            Bottom
          </div>
        </div>
      </div>
    </div>
  )
}

export function Product3DViewer({
  modelUrl,
  productName,
  className = "",
  autoRotate = true,
  showControls = true
}: Product3DViewerProps) {
  const [isAutoRotating, setIsAutoRotating] = useState(autoRotate)
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [rotation, setRotation] = useState({ x: 0, y: 0 })
  const [isDragging, setIsDragging] = useState(false)
  const [lastMousePos, setLastMousePos] = useState({ x: 0, y: 0 })
  const viewerRef = useRef<HTMLDivElement>(null)

  // Auto rotation effect
  useEffect(() => {
    if (!isAutoRotating) return

    const interval = setInterval(() => {
      setRotation(prev => ({ ...prev, y: prev.y + 1 }))
    }, 50)

    return () => clearInterval(interval)
  }, [isAutoRotating])

  const handleMouseDown = (e: React.MouseEvent) => {
    setIsDragging(true)
    setLastMousePos({ x: e.clientX, y: e.clientY })
    setIsAutoRotating(false)
  }

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!isDragging) return

    const deltaX = e.clientX - lastMousePos.x
    const deltaY = e.clientY - lastMousePos.y

    setRotation(prev => ({
      x: Math.max(-90, Math.min(90, prev.x - deltaY * 0.5)),
      y: prev.y + deltaX * 0.5
    }))

    setLastMousePos({ x: e.clientX, y: e.clientY })
  }

  const handleMouseUp = () => {
    setIsDragging(false)
  }

  const resetView = () => {
    setRotation({ x: 0, y: 0 })
  }

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen)
  }

  const ViewerContent = () => (
    <div className={`relative ${isFullscreen ? 'fixed inset-0 z-50 bg-white' : 'aspect-square'}`}>
      <div
        ref={viewerRef}
        className="w-full h-full bg-gradient-to-br from-slate-50 to-slate-100 rounded-lg overflow-hidden cursor-grab active:cursor-grabbing"
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseUp}
        style={{
          perspective: '1000px'
        }}
      >
        <div
          className="w-full h-full flex items-center justify-center"
          style={{
            transform: `rotateX(${rotation.x}deg) rotateY(${rotation.y}deg)`,
            transformStyle: 'preserve-3d',
            transition: isDragging ? 'none' : 'transform 0.1s ease-out'
          }}
        >
          <CSS3DCube productName={productName} autoRotate={false} />
        </div>
      </div>

      {/* 3D Viewer Controls */}
      {showControls && (
        <div className="absolute top-4 right-4 flex flex-col gap-2">
          <Button
            variant="secondary"
            size="sm"
            onClick={() => setIsAutoRotating(!isAutoRotating)}
            className="bg-white/90 backdrop-blur-sm"
          >
            {isAutoRotating ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
          </Button>

          <Button
            variant="secondary"
            size="sm"
            onClick={resetView}
            className="bg-white/90 backdrop-blur-sm"
          >
            <RotateCcw className="h-4 w-4" />
          </Button>

          <Button
            variant="secondary"
            size="sm"
            onClick={toggleFullscreen}
            className="bg-white/90 backdrop-blur-sm"
          >
            {isFullscreen ? <Minimize2 className="h-4 w-4" /> : <Maximize2 className="h-4 w-4" />}
          </Button>
        </div>
      )}

      {/* 3D Badge */}
      <div className="absolute top-4 left-4">
        <Badge className="bg-gradient-to-r from-blue-500 to-purple-600 text-white">
          <Cube className="h-3 w-3 mr-1" />
          3D View
        </Badge>
      </div>

      {/* Instructions */}
      <div className="absolute bottom-4 left-4 right-4">
        <Card className="bg-white/90 backdrop-blur-sm">
          <CardContent className="p-3">
            <div className="text-xs text-slate-600 text-center">
              <div className="flex items-center justify-center gap-4 text-xs">
                <span>🖱️ Drag to rotate</span>
                <span>▶️ Click play for auto-rotate</span>
                <span>🔄 Reset view</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Fullscreen close button */}
      {isFullscreen && (
        <Button
          variant="ghost"
          size="sm"
          onClick={toggleFullscreen}
          className="absolute top-4 left-1/2 transform -translate-x-1/2 bg-white/90 backdrop-blur-sm"
        >
          <Minimize2 className="h-4 w-4 mr-2" />
          Exit Fullscreen
        </Button>
      )}
    </div>
  )

  return (
    <div className={className}>
      <ViewerContent />

      {/* Add custom CSS for 3D transforms */}
      <style jsx>{`
        @keyframes spin3d {
          from {
            transform: rotateY(0deg);
          }
          to {
            transform: rotateY(360deg);
          }
        }

        .transform-style-preserve-3d {
          transform-style: preserve-3d;
        }

        .perspective-1000 {
          perspective: 1000px;
        }

        .translate-z-16 {
          transform: translateZ(64px);
        }

        .rotate-y-90 {
          transform: rotateY(90deg) translateZ(64px);
        }

        .rotate-y-180 {
          transform: rotateY(180deg) translateZ(64px);
        }

        .rotate-y-270 {
          transform: rotateY(-90deg) translateZ(64px);
        }

        .rotate-x-90 {
          transform: rotateX(90deg) translateZ(64px);
        }

        .rotate-x-270 {
          transform: rotateX(-90deg) translateZ(64px);
        }
      `}</style>
    </div>
  )
}

// Hook for checking 3D support
export function use3DSupport() {
  const [isSupported, setIsSupported] = useState(true)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Check for CSS 3D transform support
    try {
      const testElement = document.createElement('div')
      testElement.style.transform = 'translateZ(0)'
      setIsSupported(testElement.style.transform !== '')
    } catch (e) {
      setIsSupported(false)
    }
    setIsLoading(false)
  }, [])

  return { isSupported, isLoading }
}
