"use client"

import { useState } from "react"
import Link from "next/link"
import { Search, ShoppingCart, User, Menu, X, Heart } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { useCart } from "@/components/cart-context"
import { useWishlist } from "@/components/wishlist-context"
import { Input } from "@/components/ui/input"
import { SearchBar } from "@/components/search-bar"

export function Header() {
  const { itemCount } = useCart()
  const { itemCount: wishlistCount } = useWishlist()
  const [isMenuOpen, setIsMenuOpen] = useState(false)

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-white/95 backdrop-blur supports-[backdrop-filter]:bg-white/60">
      <div className="container mx-auto px-4">
        <div className="flex h-16 items-center justify-between">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-2">
            <div className="h-8 w-8 rounded-lg bg-gradient-to-br from-slate-900 to-slate-700" />
            <span className="text-xl font-bold text-slate-900">ModernStore</span>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            <Link
              href="/products"
              className="text-sm font-medium text-slate-700 hover:text-slate-900 transition-colors"
            >
              Products
            </Link>
            <Link
              href="/categories"
              className="text-sm font-medium text-slate-700 hover:text-slate-900 transition-colors"
            >
              Categories
            </Link>
            <Link href="/about" className="text-sm font-medium text-slate-700 hover:text-slate-900 transition-colors">
              About
            </Link>
            <Link href="/contact" className="text-sm font-medium text-slate-700 hover:text-slate-900 transition-colors">
              Contact
            </Link>
          </nav>

          {/* Search Bar */}
          <div className="hidden md:flex items-center space-x-4 flex-1 max-w-md mx-8">
            <SearchBar className="w-full" />
          </div>

          {/* Actions */}
          <div className="flex items-center space-x-4">
            <Link href="/account">
              <Button variant="ghost" size="sm" className="hidden md:flex">
                <User className="h-4 w-4" />
                <span className="ml-2">Account</span>
              </Button>
            </Link>
            <Link href="/wishlist">
              <Button variant="ghost" size="sm" className="relative">
                <Heart className="h-4 w-4" />
                <span className="ml-2 hidden sm:inline">Wishlist</span>
                {wishlistCount > 0 && (
                  <span className="absolute -top-1 -right-1 h-5 w-5 rounded-full bg-red-500 text-xs text-white flex items-center justify-center">
                    {wishlistCount}
                  </span>
                )}
              </Button>
            </Link>
            <Link href="/cart">
              <Button variant="ghost" size="sm" className="relative">
                <ShoppingCart className="h-4 w-4" />
                <span className="ml-2 hidden sm:inline">Cart</span>
                <span className="absolute -top-1 -right-1 h-5 w-5 rounded-full bg-slate-900 text-xs text-white flex items-center justify-center">
                  {itemCount}
                </span>
              </Button>
            </Link>

            {/* Mobile Menu Button */}
            <Button variant="ghost" size="sm" className="md:hidden" onClick={() => setIsMenuOpen(!isMenuOpen)}>
              {isMenuOpen ? <X className="h-4 w-4" /> : <Menu className="h-4 w-4" />}
            </Button>
          </div>
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <div className="md:hidden border-t bg-white py-4">
            <div className="flex flex-col space-y-4">
              <SearchBar showSuggestions={false} />
              <nav className="flex flex-col space-y-2">
                <Link href="/products" className="text-sm font-medium text-slate-700 hover:text-slate-900 py-2">
                  Products
                </Link>
                <Link href="/categories" className="text-sm font-medium text-slate-700 hover:text-slate-900 py-2">
                  Categories
                </Link>
                <Link href="/about" className="text-sm font-medium text-slate-700 hover:text-slate-900 py-2">
                  About
                </Link>
                <Link href="/contact" className="text-sm font-medium text-slate-700 hover:text-slate-900 py-2">
                  Contact
                </Link>
                <Link href="/account" className="text-sm font-medium text-slate-700 hover:text-slate-900 py-2">
                  Account
                </Link>
              </nav>
            </div>
          </div>
        )}
      </div>
    </header>
  )
}
