"use client"

import { useEffect } from "react"

interface PerformanceMetrics {
  fcp?: number // First Contentful Paint
  lcp?: number // Largest Contentful Paint
  fid?: number // First Input Delay
  cls?: number // Cumulative Layout Shift
  ttfb?: number // Time to First Byte
}

export function PerformanceMonitor() {
  useEffect(() => {
    if (typeof window === "undefined") return

    const metrics: PerformanceMetrics = {}

    // Measure Core Web Vitals
    const measureWebVitals = () => {
      // First Contentful Paint
      const fcpEntry = performance.getEntriesByName("first-contentful-paint")[0] as PerformanceEntry
      if (fcpEntry) {
        metrics.fcp = fcpEntry.startTime
      }

      // Time to First Byte
      const navigationEntry = performance.getEntriesByType("navigation")[0] as PerformanceNavigationTiming
      if (navigationEntry) {
        metrics.ttfb = navigationEntry.responseStart - navigationEntry.requestStart
      }

      // Largest Contentful Paint
      if ("PerformanceObserver" in window) {
        try {
          const lcpObserver = new PerformanceObserver((list) => {
            const entries = list.getEntries()
            const lastEntry = entries[entries.length - 1] as any
            if (lastEntry) {
              metrics.lcp = lastEntry.startTime
            }
          })
          lcpObserver.observe({ entryTypes: ["largest-contentful-paint"] })

          // First Input Delay
          const fidObserver = new PerformanceObserver((list) => {
            const entries = list.getEntries()
            entries.forEach((entry: any) => {
              if (entry.processingStart && entry.startTime) {
                metrics.fid = entry.processingStart - entry.startTime
              }
            })
          })
          fidObserver.observe({ entryTypes: ["first-input"] })

          // Cumulative Layout Shift
          let clsValue = 0
          const clsObserver = new PerformanceObserver((list) => {
            const entries = list.getEntries()
            entries.forEach((entry: any) => {
              if (!entry.hadRecentInput) {
                clsValue += entry.value
                metrics.cls = clsValue
              }
            })
          })
          clsObserver.observe({ entryTypes: ["layout-shift"] })

          // Clean up observers after 10 seconds
          setTimeout(() => {
            lcpObserver.disconnect()
            fidObserver.disconnect()
            clsObserver.disconnect()
          }, 10000)
        } catch (error) {
          console.warn("Performance Observer not supported:", error)
        }
      }
    }

    // Send metrics to analytics (simulated)
    const sendMetrics = () => {
      if (process.env.NODE_ENV === "development") {
        console.log("Performance Metrics:", metrics)
      }

      // In production, you would send these to your analytics service
      // Example: analytics.track("performance_metrics", metrics)
    }

    // Measure on page load
    if (document.readyState === "complete") {
      measureWebVitals()
      setTimeout(sendMetrics, 1000)
    } else {
      window.addEventListener("load", () => {
        measureWebVitals()
        setTimeout(sendMetrics, 1000)
      })
    }

    // Measure resource loading performance
    const measureResourcePerformance = () => {
      const resources = performance.getEntriesByType("resource") as PerformanceResourceTiming[]
      const slowResources = resources.filter(resource => resource.duration > 1000)
      
      if (slowResources.length > 0 && process.env.NODE_ENV === "development") {
        console.warn("Slow loading resources:", slowResources.map(r => ({
          name: r.name,
          duration: r.duration,
          size: r.transferSize
        })))
      }
    }

    setTimeout(measureResourcePerformance, 2000)

    // Monitor memory usage (if available)
    const monitorMemory = () => {
      if ("memory" in performance) {
        const memory = (performance as any).memory
        if (process.env.NODE_ENV === "development") {
          console.log("Memory Usage:", {
            used: Math.round(memory.usedJSHeapSize / 1048576) + " MB",
            total: Math.round(memory.totalJSHeapSize / 1048576) + " MB",
            limit: Math.round(memory.jsHeapSizeLimit / 1048576) + " MB"
          })
        }
      }
    }

    const memoryInterval = setInterval(monitorMemory, 30000) // Check every 30 seconds

    return () => {
      clearInterval(memoryInterval)
    }
  }, [])

  return null // This component doesn't render anything
}

// Utility function to preload critical resources
export function preloadCriticalResources() {
  if (typeof window === "undefined") return

  const criticalResources = [
    "/fonts/geist-sans.woff2",
    "/fonts/geist-mono.woff2",
    "/3d-product-showcase-hero-image.jpg"
  ]

  criticalResources.forEach(resource => {
    const link = document.createElement("link")
    link.rel = "preload"
    link.href = resource
    
    if (resource.includes(".woff2")) {
      link.as = "font"
      link.type = "font/woff2"
      link.crossOrigin = "anonymous"
    } else if (resource.includes(".jpg") || resource.includes(".png")) {
      link.as = "image"
    }
    
    document.head.appendChild(link)
  })
}

// Utility function to prefetch next page resources
export function prefetchNextPageResources(urls: string[]) {
  if (typeof window === "undefined") return

  urls.forEach(url => {
    const link = document.createElement("link")
    link.rel = "prefetch"
    link.href = url
    document.head.appendChild(link)
  })
}

// Hook for measuring component render performance
export function useRenderPerformance(componentName: string) {
  useEffect(() => {
    const startTime = performance.now()
    
    return () => {
      const endTime = performance.now()
      const renderTime = endTime - startTime
      
      if (renderTime > 16 && process.env.NODE_ENV === "development") {
        console.warn(`${componentName} render took ${renderTime.toFixed(2)}ms (>16ms)`)
      }
    }
  })
}
