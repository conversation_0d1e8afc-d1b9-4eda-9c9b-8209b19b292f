"use client"

import { ProductCarousel } from "@/components/product-carousel"
import { mockProducts } from "@/lib/mock-data"
import { Product } from "@/lib/types"

export function FeaturedProducts() {
  // Get featured products (products with high ratings or marked as featured)
  const featuredProducts = mockProducts
    .filter(product => product.rating >= 4.5 || product.featured)
    .slice(0, 8) // Limit to 8 products for better performance

  return (
    <ProductCarousel
      products={featuredProducts}
      title="Featured Products"
      subtitle="Discover our handpicked selection of premium products with exceptional quality and customer satisfaction"
      autoPlay={true}
      autoPlayInterval={5000}
      showControls={true}
      itemsPerView={{
        mobile: 1,
        tablet: 2,
        desktop: 4
      }}
      className="bg-white"
    />
  )
}

export function NewArrivals() {
  // Get newest products (simulate by taking last few products)
  const newProducts = mockProducts
    .slice(-6) // Get last 6 products as "new arrivals"
    .reverse() // Show newest first

  return (
    <ProductCarousel
      products={newProducts}
      title="New Arrivals"
      subtitle="Be the first to discover our latest collection of cutting-edge products"
      autoPlay={true}
      autoPlayInterval={6000}
      showControls={true}
      itemsPerView={{
        mobile: 1,
        tablet: 2,
        desktop: 3
      }}
      className="bg-gradient-to-br from-slate-50 to-white"
    />
  )
}

export function BestSellers() {
  // Get best selling products (simulate by products with high ratings and lower stock)
  const bestSellers = mockProducts
    .filter(product => product.rating >= 4.0)
    .sort((a, b) => b.rating - a.rating)
    .slice(0, 6)

  return (
    <ProductCarousel
      products={bestSellers}
      title="Best Sellers"
      subtitle="Join thousands of satisfied customers who love these top-rated products"
      autoPlay={true}
      autoPlayInterval={4500}
      showControls={true}
      itemsPerView={{
        mobile: 1,
        tablet: 2,
        desktop: 3
      }}
      className="bg-gradient-to-br from-blue-50 to-indigo-50"
    />
  )
}

export function SaleProducts() {
  // Get products on sale
  const saleProducts = mockProducts
    .filter(product => product.salePrice)
    .slice(0, 6)

  if (saleProducts.length === 0) {
    return null // Don't render if no sale products
  }

  return (
    <ProductCarousel
      products={saleProducts}
      title="Special Offers"
      subtitle="Limited time deals on premium products - save big while stocks last!"
      autoPlay={true}
      autoPlayInterval={3500}
      showControls={true}
      itemsPerView={{
        mobile: 1,
        tablet: 2,
        desktop: 4
      }}
      className="bg-gradient-to-br from-red-50 to-orange-50"
    />
  )
}
