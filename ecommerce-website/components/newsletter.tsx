import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Mail } from "lucide-react"

export function Newsletter() {
  return (
    <section className="py-16 bg-slate-900">
      <div className="container mx-auto px-4">
        <div className="max-w-2xl mx-auto text-center">
          <div className="mb-8">
            <Mail className="h-12 w-12 text-white mx-auto mb-4" />
            <h2 className="text-3xl lg:text-4xl font-bold text-white mb-4">Stay Updated</h2>
            <p className="text-lg text-slate-300">
              Subscribe to our newsletter and be the first to know about new products, exclusive deals, and 3D
              experiences
            </p>
          </div>

          <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
            <Input type="email" placeholder="Enter your email" className="bg-white border-slate-300 flex-1" />
            <Button className="bg-white text-slate-900 hover:bg-slate-100">Subscribe</Button>
          </div>

          <p className="text-sm text-slate-400 mt-4">No spam, unsubscribe at any time. We respect your privacy.</p>
        </div>
      </div>
    </section>
  )
}
