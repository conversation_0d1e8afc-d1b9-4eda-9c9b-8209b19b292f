"use client"

import { useState, useEffect, useRef } from "react"
import Link from "next/link"
import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { WishlistButton } from "@/components/wishlist-button"
import { useCart } from "@/components/cart-context"
import { ChevronLeft, ChevronRight, ShoppingCart, Star, Play, Pause } from "lucide-react"
import { Product } from "@/lib/types"
import { cn } from "@/lib/utils"
import { toast } from "sonner"

interface ProductCarouselProps {
  products: Product[]
  title?: string
  subtitle?: string
  autoPlay?: boolean
  autoPlayInterval?: number
  showControls?: boolean
  itemsPerView?: {
    mobile: number
    tablet: number
    desktop: number
  }
  className?: string
}

export function ProductCarousel({
  products,
  title = "Featured Products",
  subtitle = "Discover our handpicked selection of premium products",
  autoPlay = true,
  autoPlayInterval = 4000,
  showControls = true,
  itemsPerView = {
    mobile: 1,
    tablet: 2,
    desktop: 4
  },
  className = ""
}: ProductCarouselProps) {
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isPlaying, setIsPlaying] = useState(autoPlay)
  const [isHovered, setIsHovered] = useState(false)
  const intervalRef = useRef<NodeJS.Timeout | null>(null)
  const carouselRef = useRef<HTMLDivElement>(null)
  const { addItem } = useCart()

  // Calculate items per view based on screen size
  const getItemsPerView = () => {
    if (typeof window === 'undefined') return itemsPerView.desktop
    
    if (window.innerWidth < 768) return itemsPerView.mobile
    if (window.innerWidth < 1024) return itemsPerView.tablet
    return itemsPerView.desktop
  }

  const [currentItemsPerView, setCurrentItemsPerView] = useState(getItemsPerView())

  // Handle window resize
  useEffect(() => {
    const handleResize = () => {
      setCurrentItemsPerView(getItemsPerView())
    }

    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  // Auto-play functionality
  useEffect(() => {
    if (isPlaying && !isHovered && products.length > currentItemsPerView) {
      intervalRef.current = setInterval(() => {
        setCurrentIndex(prev => {
          const maxIndex = Math.max(0, products.length - currentItemsPerView)
          return prev >= maxIndex ? 0 : prev + 1
        })
      }, autoPlayInterval)
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }
    }
  }, [isPlaying, isHovered, products.length, currentItemsPerView, autoPlayInterval])

  const goToSlide = (index: number) => {
    const maxIndex = Math.max(0, products.length - currentItemsPerView)
    setCurrentIndex(Math.min(Math.max(0, index), maxIndex))
  }

  const goToPrevious = () => {
    setCurrentIndex(prev => {
      const maxIndex = Math.max(0, products.length - currentItemsPerView)
      return prev <= 0 ? maxIndex : prev - 1
    })
  }

  const goToNext = () => {
    setCurrentIndex(prev => {
      const maxIndex = Math.max(0, products.length - currentItemsPerView)
      return prev >= maxIndex ? 0 : prev + 1
    })
  }

  const togglePlayPause = () => {
    setIsPlaying(!isPlaying)
  }

  const handleAddToCart = (product: Product, e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    addItem(product)
    toast.success(`${product.name} added to cart!`)
  }

  const maxIndex = Math.max(0, products.length - currentItemsPerView)

  return (
    <section className={cn("py-16 bg-gradient-to-br from-slate-50 to-white", className)}>
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-12">
          <h2 className="text-3xl lg:text-4xl font-bold text-slate-900 mb-4">
            {title}
          </h2>
          <p className="text-lg text-slate-600 max-w-2xl mx-auto">
            {subtitle}
          </p>
        </div>

        {/* Carousel Container */}
        <div 
          className="relative"
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
        >
          {/* Navigation Controls */}
          {showControls && products.length > currentItemsPerView && (
            <>
              <Button
                variant="outline"
                size="icon"
                className="absolute left-4 top-1/2 -translate-y-1/2 z-10 bg-white/90 backdrop-blur-sm border-slate-200 hover:bg-white shadow-lg"
                onClick={goToPrevious}
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              
              <Button
                variant="outline"
                size="icon"
                className="absolute right-4 top-1/2 -translate-y-1/2 z-10 bg-white/90 backdrop-blur-sm border-slate-200 hover:bg-white shadow-lg"
                onClick={goToNext}
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </>
          )}

          {/* Play/Pause Control */}
          {autoPlay && showControls && (
            <Button
              variant="outline"
              size="sm"
              className="absolute top-4 right-4 z-10 bg-white/90 backdrop-blur-sm border-slate-200 hover:bg-white"
              onClick={togglePlayPause}
            >
              {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
            </Button>
          )}

          {/* Carousel Track */}
          <div 
            ref={carouselRef}
            className="overflow-hidden rounded-xl"
          >
            <div 
              className="flex transition-transform duration-500 ease-in-out"
              style={{
                transform: `translateX(-${currentIndex * (100 / currentItemsPerView)}%)`,
                width: `${(products.length / currentItemsPerView) * 100}%`
              }}
            >
              {products.map((product) => (
                <div 
                  key={product.id}
                  className="flex-shrink-0 px-3"
                  style={{ width: `${100 / products.length}%` }}
                >
                  <Link href={`/products/${product.slug}`}>
                    <Card className="group cursor-pointer hover:shadow-xl transition-all duration-300 border-slate-200 overflow-hidden">
                      <CardContent className="p-0">
                        {/* Product Image */}
                        <div className="relative aspect-square overflow-hidden bg-slate-100">
                          <img
                            src={product.images[0] || "/placeholder.svg"}
                            alt={product.name}
                            className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                          />
                          
                          {/* Wishlist Button */}
                          <div className="absolute top-3 right-3">
                            <WishlistButton product={product} />
                          </div>

                          {/* Sale Badge */}
                          {product.salePrice && (
                            <Badge className="absolute top-3 left-3 bg-red-500 hover:bg-red-600">
                              Sale
                            </Badge>
                          )}

                          {/* Quick Add to Cart */}
                          <div className="absolute bottom-3 left-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <Button
                              size="sm"
                              className="w-full bg-slate-900 hover:bg-slate-800 text-white"
                              onClick={(e) => handleAddToCart(product, e)}
                            >
                              <ShoppingCart className="h-4 w-4 mr-2" />
                              Add to Cart
                            </Button>
                          </div>
                        </div>

                        {/* Product Info */}
                        <div className="p-4 space-y-2">
                          <h3 className="font-semibold text-slate-900 line-clamp-2 group-hover:text-slate-700 transition-colors">
                            {product.name}
                          </h3>
                          
                          {/* Rating */}
                          <div className="flex items-center gap-1">
                            <div className="flex items-center">
                              {[...Array(5)].map((_, i) => (
                                <Star
                                  key={i}
                                  className={cn(
                                    "h-3 w-3",
                                    i < Math.floor(product.rating)
                                      ? "text-yellow-400 fill-current"
                                      : "text-slate-300"
                                  )}
                                />
                              ))}
                            </div>
                            <span className="text-xs text-slate-500">
                              ({product.rating})
                            </span>
                          </div>

                          {/* Price */}
                          <div className="flex items-center gap-2">
                            <span className="text-lg font-bold text-slate-900">
                              ${product.salePrice || product.price}
                            </span>
                            {product.salePrice && (
                              <span className="text-sm text-slate-500 line-through">
                                ${product.price}
                              </span>
                            )}
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </Link>
                </div>
              ))}
            </div>
          </div>

          {/* Dots Indicator */}
          {showControls && products.length > currentItemsPerView && (
            <div className="flex justify-center mt-6 space-x-2">
              {Array.from({ length: maxIndex + 1 }).map((_, index) => (
                <button
                  key={index}
                  className={cn(
                    "w-2 h-2 rounded-full transition-all duration-300",
                    index === currentIndex
                      ? "bg-slate-900 w-6"
                      : "bg-slate-300 hover:bg-slate-400"
                  )}
                  onClick={() => goToSlide(index)}
                />
              ))}
            </div>
          )}
        </div>

        {/* Progress Bar */}
        {autoPlay && isPlaying && (
          <div className="mt-4 w-full bg-slate-200 rounded-full h-1 overflow-hidden">
            <div 
              className="h-full bg-slate-900 rounded-full transition-all duration-100 ease-linear"
              style={{
                width: `${((Date.now() % autoPlayInterval) / autoPlayInterval) * 100}%`,
                animation: `carousel-progress ${autoPlayInterval}ms linear infinite`
              }}
            />
          </div>
        )}
      </div>

      {/* CSS for progress bar animation */}
      <style jsx>{`
        @keyframes carousel-progress {
          from { width: 0%; }
          to { width: 100%; }
        }
      `}</style>
    </section>
  )
}
