// Service Worker for ModernStore PWA
const CACHE_NAME = 'modernstore-v1'
const STATIC_CACHE_NAME = 'modernstore-static-v1'
const DYNAMIC_CACHE_NAME = 'modernstore-dynamic-v1'

// Assets to cache on install
const STATIC_ASSETS = [
  '/',
  '/products',
  '/categories',
  '/about',
  '/contact',
  '/manifest.json',
  '/icon-192x192.png',
  '/icon-512x512.png',
  '/3d-product-showcase-hero-image.jpg'
]

// Install event - cache static assets
self.addEventListener('install', (event) => {
  console.log('Service Worker installing...')
  event.waitUntil(
    caches.open(STATIC_CACHE_NAME)
      .then((cache) => {
        console.log('Caching static assets')
        return cache.addAll(STATIC_ASSETS)
      })
      .then(() => {
        return self.skipWaiting()
      })
  )
})

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  console.log('Service Worker activating...')
  event.waitUntil(
    caches.keys()
      .then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (cacheName !== STATIC_CACHE_NAME && cacheName !== DYNAMIC_CACHE_NAME) {
              console.log('Deleting old cache:', cacheName)
              return caches.delete(cacheName)
            }
          })
        )
      })
      .then(() => {
        return self.clients.claim()
      })
  )
})

// Fetch event - serve from cache, fallback to network
self.addEventListener('fetch', (event) => {
  const { request } = event
  const url = new URL(request.url)

  // Skip non-GET requests
  if (request.method !== 'GET') {
    return
  }

  // Skip external requests
  if (url.origin !== location.origin) {
    return
  }

  event.respondWith(
    caches.match(request)
      .then((cachedResponse) => {
        if (cachedResponse) {
          return cachedResponse
        }

        return fetch(request)
          .then((response) => {
            // Don't cache non-successful responses
            if (!response || response.status !== 200 || response.type !== 'basic') {
              return response
            }

            // Clone the response
            const responseToCache = response.clone()

            // Cache dynamic content
            caches.open(DYNAMIC_CACHE_NAME)
              .then((cache) => {
                cache.put(request, responseToCache)
              })

            return response
          })
          .catch(() => {
            // Return offline page for navigation requests
            if (request.destination === 'document') {
              return caches.match('/offline.html')
            }
          })
      })
  )
})

// Background sync for offline actions
self.addEventListener('sync', (event) => {
  console.log('Background sync triggered:', event.tag)
  
  if (event.tag === 'cart-sync') {
    event.waitUntil(syncCart())
  }
  
  if (event.tag === 'wishlist-sync') {
    event.waitUntil(syncWishlist())
  }
})

// Push notification handling
self.addEventListener('push', (event) => {
  console.log('Push notification received:', event)
  
  const options = {
    body: 'Check out our latest products!',
    icon: '/icon-192x192.png',
    badge: '/icon-192x192.png',
    vibrate: [100, 50, 100],
    data: {
      dateOfArrival: Date.now(),
      primaryKey: 1
    },
    actions: [
      {
        action: 'explore',
        title: 'Explore Products',
        icon: '/icon-192x192.png'
      },
      {
        action: 'close',
        title: 'Close',
        icon: '/icon-192x192.png'
      }
    ]
  }

  if (event.data) {
    const data = event.data.json()
    options.body = data.body || options.body
    options.data = { ...options.data, ...data }
  }

  event.waitUntil(
    self.registration.showNotification('ModernStore', options)
  )
})

// Notification click handling
self.addEventListener('notificationclick', (event) => {
  console.log('Notification clicked:', event)
  
  event.notification.close()

  if (event.action === 'explore') {
    event.waitUntil(
      clients.openWindow('/products')
    )
  } else if (event.action === 'close') {
    // Just close the notification
    return
  } else {
    // Default action - open the app
    event.waitUntil(
      clients.openWindow('/')
    )
  }
})

// Helper functions
async function syncCart() {
  try {
    const cartData = await getStoredData('modernstore.cart.v1')
    if (cartData) {
      // In a real app, you would sync with your backend
      console.log('Syncing cart data:', cartData)
    }
  } catch (error) {
    console.error('Cart sync failed:', error)
  }
}

async function syncWishlist() {
  try {
    const wishlistData = await getStoredData('modernstore.wishlist.v1')
    if (wishlistData) {
      // In a real app, you would sync with your backend
      console.log('Syncing wishlist data:', wishlistData)
    }
  } catch (error) {
    console.error('Wishlist sync failed:', error)
  }
}

async function getStoredData(key) {
  return new Promise((resolve) => {
    // This is a simplified version - in reality you'd need to communicate with the main thread
    resolve(null)
  })
}

// Periodic background sync (if supported)
self.addEventListener('periodicsync', (event) => {
  if (event.tag === 'content-sync') {
    event.waitUntil(syncContent())
  }
})

async function syncContent() {
  try {
    // Sync product data, prices, inventory, etc.
    console.log('Syncing content in background')
  } catch (error) {
    console.error('Content sync failed:', error)
  }
}
